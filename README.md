# Advanced Log Manager

一个功能强大的日志管理前端应用，使用 Vue 3 构建，具有实时监控、高级搜索和全面分析功能。

## 🚀 功能特性

### 核心功能
- **实时日志流** - 类似 Papertrail 的实时 tail 功能
- **强大的搜索和过滤** - 借鉴 Splunk 的查询语言和 Elasticsearch 的全文搜索
- **可视化仪表板** - 参考 Grafana 和 Kibana 的图表展示
- **智能日志解析** - 自动识别常见日志格式和模式
- **告警和监控** - 实时异常检测和智能通知系统
- **多维度分析** - 日志统计、趋势分析、错误率监控

### 高级功能
- **智能分析引擎** - AI驱动的日志模式识别和异常检测
- **高级过滤器** - 正则表达式、时间范围、多条件组合过滤
- **告警管理系统** - 自定义告警规则、多种通知方式
- **导出和备份** - 多格式导出、自动备份、压缩选项
- **可视化分析** - 实时图表、热力图、预测分析
- **系统监控** - 实时系统指标、服务状态监控

### 企业级功能
- **流处理引擎** - 实时日志流处理、数据增强、规则引擎
- **团队协作** - 实时聊天、共享视图、日志标注、权限管理
- **查询构建器** - 可视化查询、SQL模式、自然语言查询
- **性能监控** - 性能分析、优化建议、基准测试
- **模板管理** - 日志解析模板、预定义模板库、模板测试
- **安全分析** - 威胁检测、IP情报、安全规则、合规报告

### 用户界面
- **现代化设计** - 响应式布局，支持深色/浅色主题
- **直观的导航** - 侧边栏导航，快速访问各功能模块
- **高性能** - 虚拟滚动，处理大量日志数据
- **可定制** - 灵活的过滤器和显示选项

### 技术特性
- **Vue 3 Composition API** - 现代化的 Vue.js 开发
- **TypeScript 支持** - 类型安全的开发体验
- **Pinia 状态管理** - 轻量级、直观的状态管理
- **TailwindCSS** - 实用优先的 CSS 框架
- **Chart.js** - 丰富的图表可视化
- **Vite** - 快速的构建工具

## 📦 安装和运行

### 环境要求
- Node.js 16+ 
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```
应用将在 http://localhost:3000 启动

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🎯 主要页面

### 1. 仪表板 (Dashboard)
- 关键指标概览和实时统计
- 日志量趋势图表和预测
- 日志级别分布和热力图
- 错误统计和最近错误列表
- 系统监控和服务状态
- 实时性能指标展示

### 2. 日志查看器 (Log Viewer)
- 实时日志流和自动刷新
- 高级搜索和智能过滤
- 正则表达式和多条件过滤
- 分页浏览和虚拟滚动
- 日志详情查看和上下文分析
- 多格式导出功能

### 3. 数据分析 (Analytics)
- **概览分析** - 错误趋势、响应时间、服务健康
- **智能分析** - AI驱动的模式识别和异常检测
- **可视化分析** - 实时图表、热力图、预测分析
- **告警管理** - 自定义告警规则和通知系统
- **导出管理** - 高级导出配置和备份管理
- **高级过滤** - 复杂查询和条件组合
- **流处理** - 实时日志流处理和数据增强
- **团队协作** - 实时协作、共享视图、权限管理
- **查询构建** - 可视化查询构建器和SQL编辑器
- **性能监控** - 系统性能分析和优化建议
- **模板管理** - 日志解析模板和规则管理
- **安全分析** - 威胁检测、安全监控、合规报告

### 4. 系统设置 (Settings)
- 主题切换和界面定制
- 自动刷新和实时配置
- 显示选项和性能设置
- 数据管理和清理工具
- 导出配置和备份策略

## 🔧 核心组件

### 状态管理
- `logStore.js` - 日志数据管理
- `appStore.js` - 应用设置和主题管理

### 主要组件
- `LogViewer.vue` - 主日志查看器
- `SearchBar.vue` - 搜索和过滤组件
- `LogList.vue` - 日志列表展示
- `Dashboard.vue` - 仪表板组件
- `Charts/` - 各种图表组件

### 工具函数
- `mockData.js` - 模拟数据生成
- `utils/` - 通用工具函数

## 🎨 设计理念

本项目参考了业界最佳的日志管理工具：

- **ELK Stack** - 强大的搜索和索引能力
- **Splunk** - 高级查询语言和分析功能
- **Grafana** - 优秀的可视化和仪表板设计
- **Datadog** - 现代化的用户界面和实时监控
- **Papertrail** - 简洁直观的日志浏览体验

## 🚀 特色功能

### 实时监控
- 自动刷新日志数据
- 实时错误告警
- 服务状态监控

### 高级搜索
- 全文搜索
- 正则表达式支持
- 多字段过滤
- 搜索建议

### 数据可视化
- 交互式图表
- 趋势分析
- 分布统计
- 自定义时间范围

### 用户体验
- 响应式设计
- 深色模式支持
- 键盘快捷键
- 导出功能

## 📊 数据模型

### 日志条目结构
```javascript
{
  id: string,
  timestamp: string,
  level: 'error' | 'warn' | 'info' | 'debug' | 'trace',
  source: string,
  service: string,
  message: string,
  metadata: {
    userId?: number,
    requestId?: string,
    ip?: string,
    responseTime?: string
  }
}
```

## 🔮 未来规划

- [ ] 集成真实的日志数据源
- [ ] 添加更多图表类型
- [ ] 实现日志告警规则
- [ ] 支持日志导入/导出
- [ ] 添加用户权限管理
- [ ] 实现日志聚合和采样
- [ ] 支持多语言国际化

## 📝 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**: 这是一个前端演示项目，使用模拟数据。在生产环境中，需要连接真实的日志数据源。
