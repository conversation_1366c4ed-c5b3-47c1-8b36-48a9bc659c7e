{"name": "@types/web-bluetooth", "version": "0.0.20", "description": "TypeScript definitions for web-bluetooth", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/web-bluetooth", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urish", "url": "https://github.com/urish"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/xlozinguez"}, {"name": "<PERSON>", "githubUsername": "thegecko", "url": "https://github.com/thegecko"}, {"name": "<PERSON>", "githubUsername": "DaBs", "url": "https://github.com/DaBs"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/web-bluetooth"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "6a130e3db86a1d977ddff03e84f9f150c142902343c9fa383d3e8d4f19180d98", "typeScriptVersion": "4.5"}