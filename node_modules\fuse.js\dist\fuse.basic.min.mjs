/**
 * Fuse.js v7.1.0 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2025 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */
function t(t){return Array.isArray?Array.isArray(t):"[object Array]"===c(t)}const e=1/0;function u(t){return null==t?"":function(t){if("string"==typeof t)return t;let u=t+"";return"0"==u&&1/t==-e?"-0":u}(t)}function n(t){return"string"==typeof t}function i(t){return"number"==typeof t}function s(t){return!0===t||!1===t||function(t){return function(t){return"object"==typeof t}(t)&&null!==t}(t)&&"[object Boolean]"==c(t)}function r(t){return null!=t}function o(t){return!t.trim().length}function c(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}const h=t=>`Missing ${t} property in key`,a=t=>`Property 'weight' in key '${t}' must be a positive integer`,l=Object.prototype.hasOwnProperty;class d{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach((t=>{let u=A(t);this._keys.push(u),this._keyMap[u.id]=u,e+=u.weight})),this._keys.forEach((t=>{t.weight/=e}))}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function A(e){let u=null,i=null,s=null,r=1,o=null;if(n(e)||t(e))s=e,u=f(e),i=g(e);else{if(!l.call(e,"name"))throw new Error(h("name"));const t=e.name;if(s=t,l.call(e,"weight")&&(r=e.weight,r<=0))throw new Error(a(t));u=f(t),i=g(t),o=e.getFn}return{path:u,id:i,weight:r,src:s,getFn:o}}function f(e){return t(e)?e:e.split(".")}function g(e){return t(e)?e.join("."):e}var C={isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,...{useExtendedSearch:!1,getFn:function(e,o){let c=[],h=!1;const a=(e,o,l)=>{if(r(e))if(o[l]){const d=e[o[l]];if(!r(d))return;if(l===o.length-1&&(n(d)||i(d)||s(d)))c.push(u(d));else if(t(d)){h=!0;for(let t=0,e=d.length;t<e;t+=1)a(d[t],o,l+1)}else o.length&&a(d,o,l+1)}else c.push(e)};return a(e,n(o)?o.split("."):o,0),h?c:c[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1}};const F=/[^ ]+/g;class p{constructor({getFn:t=C.getFn,fieldNormWeight:e=C.fieldNormWeight}={}){this.norm=function(t=1,e=3){const u=new Map,n=Math.pow(10,e);return{get(e){const i=e.match(F).length;if(u.has(i))return u.get(i);const s=1/Math.pow(i,.5*t),r=parseFloat(Math.round(s*n)/n);return u.set(i,r),r},clear(){u.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach(((t,e)=>{this._keysMap[t.id]=e}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,n(this.docs[0])?this.docs.forEach(((t,e)=>{this._addString(t,e)})):this.docs.forEach(((t,e)=>{this._addObject(t,e)})),this.norm.clear())}add(t){const e=this.size();n(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,u=this.size();e<u;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!r(t)||o(t))return;let u={v:t,i:e,n:this.norm.get(t)};this.records.push(u)}_addObject(e,u){let i={i:u,$:{}};this.keys.forEach(((u,s)=>{let c=u.getFn?u.getFn(e):this.getFn(e,u.path);if(r(c))if(t(c)){let e=[];const u=[{nestedArrIndex:-1,value:c}];for(;u.length;){const{nestedArrIndex:i,value:s}=u.pop();if(r(s))if(n(s)&&!o(s)){let t={v:s,i:i,n:this.norm.get(s)};e.push(t)}else t(s)&&s.forEach(((t,e)=>{u.push({nestedArrIndex:e,value:t})}))}i.$[s]=e}else if(n(c)&&!o(c)){let t={v:c,n:this.norm.get(c)};i.$[s]=t}})),this.records.push(i)}toJSON(){return{keys:this.keys,records:this.records}}}function E(t,e,{getFn:u=C.getFn,fieldNormWeight:n=C.fieldNormWeight}={}){const i=new p({getFn:u,fieldNormWeight:n});return i.setKeys(t.map(A)),i.setSources(e),i.create(),i}function B(t,{errors:e=0,currentLocation:u=0,expectedLocation:n=0,distance:i=C.distance,ignoreLocation:s=C.ignoreLocation}={}){const r=e/t.length;if(s)return r;const o=Math.abs(n-u);return i?r+o/i:o?1:r}const D=32;function m(t,e,u,{location:n=C.location,distance:i=C.distance,threshold:s=C.threshold,findAllMatches:r=C.findAllMatches,minMatchCharLength:o=C.minMatchCharLength,includeMatches:c=C.includeMatches,ignoreLocation:h=C.ignoreLocation}={}){if(e.length>D)throw new Error(`Pattern length exceeds max of ${D}.`);const a=e.length,l=t.length,d=Math.max(0,Math.min(n,l));let A=s,f=d;const g=o>1||c,F=g?Array(l):[];let p;for(;(p=t.indexOf(e,f))>-1;){let t=B(e,{currentLocation:p,expectedLocation:d,distance:i,ignoreLocation:h});if(A=Math.min(t,A),f=p+a,g){let t=0;for(;t<a;)F[p+t]=1,t+=1}}f=-1;let E=[],m=1,y=a+l;const M=1<<a-1;for(let n=0;n<a;n+=1){let s=0,o=y;for(;s<o;){B(e,{errors:n,currentLocation:d+o,expectedLocation:d,distance:i,ignoreLocation:h})<=A?s=o:y=o,o=Math.floor((y-s)/2+s)}y=o;let c=Math.max(1,d-o+1),C=r?l:Math.min(d+o,l)+a,p=Array(C+2);p[C+1]=(1<<n)-1;for(let s=C;s>=c;s-=1){let r=s-1,o=u[t.charAt(r)];if(g&&(F[r]=+!!o),p[s]=(p[s+1]<<1|1)&o,n&&(p[s]|=(E[s+1]|E[s])<<1|1|E[s+1]),p[s]&M&&(m=B(e,{errors:n,currentLocation:r,expectedLocation:d,distance:i,ignoreLocation:h}),m<=A)){if(A=m,f=r,f<=d)break;c=Math.max(1,2*d-f)}}if(B(e,{errors:n+1,currentLocation:d,expectedLocation:d,distance:i,ignoreLocation:h})>A)break;E=p}const x={isMatch:f>=0,score:Math.max(.001,m)};if(g){const t=function(t=[],e=C.minMatchCharLength){let u=[],n=-1,i=-1,s=0;for(let r=t.length;s<r;s+=1){let r=t[s];r&&-1===n?n=s:r||-1===n||(i=s-1,i-n+1>=e&&u.push([n,i]),n=-1)}return t[s-1]&&s-n>=e&&u.push([n,s-1]),u}(F,o);t.length?c&&(x.indices=t):x.isMatch=!1}return x}function y(t){let e={};for(let u=0,n=t.length;u<n;u+=1){const i=t.charAt(u);e[i]=(e[i]||0)|1<<n-u-1}return e}const M=String.prototype.normalize?t=>t.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,""):t=>t;class x{constructor(t,{location:e=C.location,threshold:u=C.threshold,distance:n=C.distance,includeMatches:i=C.includeMatches,findAllMatches:s=C.findAllMatches,minMatchCharLength:r=C.minMatchCharLength,isCaseSensitive:o=C.isCaseSensitive,ignoreDiacritics:c=C.ignoreDiacritics,ignoreLocation:h=C.ignoreLocation}={}){if(this.options={location:e,threshold:u,distance:n,includeMatches:i,findAllMatches:s,minMatchCharLength:r,isCaseSensitive:o,ignoreDiacritics:c,ignoreLocation:h},t=o?t:t.toLowerCase(),t=c?M(t):t,this.pattern=t,this.chunks=[],!this.pattern.length)return;const a=(t,e)=>{this.chunks.push({pattern:t,alphabet:y(t),startIndex:e})},l=this.pattern.length;if(l>D){let t=0;const e=l%D,u=l-e;for(;t<u;)a(this.pattern.substr(t,D),t),t+=D;if(e){const t=l-D;a(this.pattern.substr(t),t)}}else a(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,ignoreDiacritics:u,includeMatches:n}=this.options;if(t=e?t:t.toLowerCase(),t=u?M(t):t,this.pattern===t){let e={isMatch:!0,score:0};return n&&(e.indices=[[0,t.length-1]]),e}const{location:i,distance:s,threshold:r,findAllMatches:o,minMatchCharLength:c,ignoreLocation:h}=this.options;let a=[],l=0,d=!1;this.chunks.forEach((({pattern:e,alphabet:u,startIndex:A})=>{const{isMatch:f,score:g,indices:C}=m(t,e,u,{location:i+A,distance:s,threshold:r,findAllMatches:o,minMatchCharLength:c,includeMatches:n,ignoreLocation:h});f&&(d=!0),l+=g,f&&C&&(a=[...a,...C])}));let A={isMatch:d,score:d?l/this.chunks.length:1};return d&&n&&(A.indices=a),A}}const L=[];function _(t,e){for(let u=0,n=L.length;u<n;u+=1){let n=L[u];if(n.condition(t,e))return new n(t,e)}return new x(t,e)}function k(t,e){const u=t.matches;e.matches=[],r(u)&&u.forEach((t=>{if(!r(t.indices)||!t.indices.length)return;const{indices:u,value:n}=t;let i={indices:u,value:n};t.key&&(i.key=t.key.src),t.idx>-1&&(i.refIndex=t.idx),e.matches.push(i)}))}function v(t,e){e.score=t.score}class w{constructor(t,e={},u){if(this.options={...C,...e},this.options.useExtendedSearch)throw new Error("Extended search is not available");this._keyStore=new d(this.options.keys),this.setCollection(t,u)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof p))throw new Error("Incorrect 'index' type");this._myIndex=e||E(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){r(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=(()=>!1)){const e=[];for(let u=0,n=this._docs.length;u<n;u+=1){const i=this._docs[u];t(i,u)&&(this.removeAt(u),u-=1,n-=1,e.push(i))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:u,includeScore:s,shouldSort:r,sortFn:o,ignoreFieldNorm:c}=this.options;let h=n(t)?n(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(t,{ignoreFieldNorm:e=C.ignoreFieldNorm}){t.forEach((t=>{let u=1;t.matches.forEach((({key:t,norm:n,score:i})=>{const s=t?t.weight:null;u*=Math.pow(0===i&&s?Number.EPSILON:i,(s||1)*(e?1:n))})),t.score=u}))}(h,{ignoreFieldNorm:c}),r&&h.sort(o),i(e)&&e>-1&&(h=h.slice(0,e)),function(t,e,{includeMatches:u=C.includeMatches,includeScore:n=C.includeScore}={}){const i=[];return u&&i.push(k),n&&i.push(v),t.map((t=>{const{idx:u}=t,n={item:e[u],refIndex:u};return i.length&&i.forEach((e=>{e(t,n)})),n}))}(h,this._docs,{includeMatches:u,includeScore:s})}_searchStringList(t){const e=_(t,this.options),{records:u}=this._myIndex,n=[];return u.forEach((({v:t,i:u,n:i})=>{if(!r(t))return;const{isMatch:s,score:o,indices:c}=e.searchIn(t);s&&n.push({item:t,idx:u,matches:[{score:o,value:t,norm:i,indices:c}]})})),n}_searchLogical(t){throw new Error("Logical search is not available")}_searchObjectList(t){const e=_(t,this.options),{keys:u,records:n}=this._myIndex,i=[];return n.forEach((({$:t,i:n})=>{if(!r(t))return;let s=[];u.forEach(((u,n)=>{s.push(...this._findMatches({key:u,value:t[n],searcher:e}))})),s.length&&i.push({idx:n,item:t,matches:s})})),i}_findMatches({key:e,value:u,searcher:n}){if(!r(u))return[];let i=[];if(t(u))u.forEach((({v:t,i:u,n:s})=>{if(!r(t))return;const{isMatch:o,score:c,indices:h}=n.searchIn(t);o&&i.push({score:c,key:e,value:t,idx:u,norm:s,indices:h})}));else{const{v:t,n:s}=u,{isMatch:r,score:o,indices:c}=n.searchIn(t);r&&i.push({score:o,key:e,value:t,norm:s,indices:c})}return i}}w.version="7.1.0",w.createIndex=E,w.parseIndex=function(t,{getFn:e=C.getFn,fieldNormWeight:u=C.fieldNormWeight}={}){const{keys:n,records:i}=t,s=new p({getFn:e,fieldNormWeight:u});return s.setKeys(n),s.setIndexRecords(i),s},w.config=C;export{w as default};