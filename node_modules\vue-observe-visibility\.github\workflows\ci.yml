# This is a basic workflow to help you get started with Actions

name: CI

# Controls when the action will run. Triggers the workflow on push or pull request
# events but only for the master branch
on:
  push:
    branches: [ next ]
  pull_request:
    branches: [ next ]

# A workflow run is made up of one or more jobs that can run sequentially or in parallel
jobs:
  # This workflow contains a single job called "build"
  build:
    # The type of runner that the job will run on
    runs-on: ubuntu-latest

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
    # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
    - uses: actions/checkout@v2
    
    - name: Get yarn cache directory path
      id: yarn-cache-dir-path
      run: echo "::set-output name=dir::$(yarn cache dir)"
    
    - name: Yarn cache
      uses: actions/cache@v1
      id: yarn-cache # use this to check for `cache-hit` (`steps.yarn-cache.outputs.cache-hit != 'true'`)
      with:
        path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
        key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
        restore-keys: |
          ${{ runner.os }}-yarn-
    
    - name: Dependencies cache
      uses: actions/cache@v2
      with:
        path: |
          node_modules
          **/node_modules
        key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}
    
    - name: Cypress cache
      uses: actions/cache@v1
      with:
        path: /home/<USER>/.cache/Cypress
        key: ${{ runner.os }}-Cypress-v1
    
    - name: Install
      run: yarn --frozen-lockfile

    # Runs a single command using the runners shell
    - name: Lint
      run: |
        yarn run lint
    
    - name: Build package
      run: |
        yarn run build

    # Runs a set of commands using the runners shell
    - name: E2E tests
      run: |
        yarn run test

    - uses: actions/upload-artifact@v1
      with:
        name: Cypress Videos
        path: cypress/videos
