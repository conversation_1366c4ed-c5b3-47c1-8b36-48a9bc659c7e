var VueObserveVisibility=function(t,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function i(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return o(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return o(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var s=function(){function t(e,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.el=e,this.observer=null,this.frozen=!1,this.createObserver(r,n)}var r,o,s;return r=t,(o=[{key:"createObserver",value:function(t,r){var n=this;if(this.observer&&this.destroyObserver(),!this.frozen){var o;if(this.options="function"==typeof(o=t)?{callback:o}:o,this.callback=function(t,e){n.options.callback(t,e),t&&n.options.once&&(n.frozen=!0,n.destroyObserver())},this.callback&&this.options.throttle){var s=(this.options.throttleOptions||{}).leading;this.callback=function(t,e){var r,n,o,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=function(l){for(var a=arguments.length,c=new Array(a>1?a-1:0),u=1;u<a;u++)c[u-1]=arguments[u];if(o=c,!r||l!==n){var f=s.leading;"function"==typeof f&&(f=f(l,n)),r&&l===n||!f||t.apply(void 0,[l].concat(i(o))),n=l,clearTimeout(r),r=setTimeout(function(){t.apply(void 0,[l].concat(i(o))),r=0},e)}};return l._clear=function(){clearTimeout(r),r=null},l}(this.callback,this.options.throttle,{leading:function(t){return"both"===s||"visible"===s&&t||"hidden"===s&&!t}})}this.oldResult=void 0,this.observer=new IntersectionObserver(function(t){var e=t[0];if(t.length>1){var r=t.find(function(t){return t.isIntersecting});r&&(e=r)}if(n.callback){var i=e.isIntersecting&&e.intersectionRatio>=n.threshold;if(i===n.oldResult)return;n.oldResult=i,n.callback(i,e)}},this.options.intersection),e.nextTick(function(){n.observer&&n.observer.observe(n.el)})}}},{key:"destroyObserver",value:function(){this.observer&&(this.observer.disconnect(),this.observer=null),this.callback&&this.callback._clear&&(this.callback._clear(),this.callback=null)}},{key:"threshold",get:function(){return this.options.intersection&&"number"==typeof this.options.intersection.threshold?this.options.intersection.threshold:0}}])&&n(r.prototype,o),s&&n(r,s),t}();function l(t,e,r){var n=e.value;if(n)if("undefined"==typeof IntersectionObserver)console.warn("[vue-observe-visibility] IntersectionObserver API is not available in your browser. Please install this polyfill: https://github.com/w3c/IntersectionObserver/tree/master/polyfill");else{var i=new s(t,n,r);t._vue_visibilityState=i}}function a(t){var e=t._vue_visibilityState;e&&(e.destroyObserver(),delete t._vue_visibilityState)}var c={beforeMount:l,updated:function(t,e,n){var i=e.value;if(!function t(e,n){if(e===n)return!0;if("object"===r(e)){for(var i in e)if(!t(e[i],n[i]))return!1;return!0}return!1}(i,e.oldValue)){var o=t._vue_visibilityState;i?o?o.createObserver(i,n):l(t,{value:i},n):a(t)}},unmounted:a};function u(t){t.directive("observe-visibility",c)}var f={version:"2.0.0-alpha.1",install:u};return t.ObserveVisibility=c,t.default=f,t.install=u,t}({},Vue);
