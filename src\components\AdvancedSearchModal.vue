<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="$emit('close')">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">高级搜索</h2>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <component :is="X" class="w-6 h-6" />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 左侧：搜索条件 -->
          <div class="space-y-6">
            <!-- 基础搜索 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">基础搜索</h3>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">关键词</label>
                  <input
                    v-model="searchForm.keywords"
                    type="text"
                    class="input w-full"
                    placeholder="输入搜索关键词，支持正则表达式"
                  />
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">日志级别</label>
                    <select v-model="searchForm.level" class="input w-full">
                      <option value="">全部级别</option>
                      <option value="error">错误</option>
                      <option value="warn">警告</option>
                      <option value="info">信息</option>
                      <option value="debug">调试</option>
                      <option value="trace">追踪</option>
                    </select>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">来源</label>
                    <select v-model="searchForm.source" class="input w-full">
                      <option value="">全部来源</option>
                      <option value="nginx">Nginx</option>
                      <option value="mysql">MySQL</option>
                      <option value="redis">Redis</option>
                      <option value="api">API</option>
                      <option value="worker">Worker</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 时间范围 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">时间范围</h3>
              <div class="space-y-4">
                <div class="flex items-center space-x-4">
                  <label class="flex items-center">
                    <input
                      v-model="searchForm.timeType"
                      type="radio"
                      value="relative"
                      class="form-radio text-blue-600"
                    />
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">相对时间</span>
                  </label>
                  <label class="flex items-center">
                    <input
                      v-model="searchForm.timeType"
                      type="radio"
                      value="absolute"
                      class="form-radio text-blue-600"
                    />
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">绝对时间</span>
                  </label>
                </div>
                
                <div v-if="searchForm.timeType === 'relative'">
                  <select v-model="searchForm.relativeTime" class="input w-full">
                    <option value="15m">最近15分钟</option>
                    <option value="1h">最近1小时</option>
                    <option value="6h">最近6小时</option>
                    <option value="24h">最近24小时</option>
                    <option value="7d">最近7天</option>
                    <option value="30d">最近30天</option>
                  </select>
                </div>
                
                <div v-else class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">开始时间</label>
                    <input
                      v-model="searchForm.startTime"
                      type="datetime-local"
                      class="input w-full"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">结束时间</label>
                    <input
                      v-model="searchForm.endTime"
                      type="datetime-local"
                      class="input w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 高级条件 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">高级条件</h3>
              <div class="space-y-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">用户ID</label>
                  <input
                    v-model="searchForm.userId"
                    type="text"
                    class="input w-full"
                    placeholder="输入用户ID"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">请求ID</label>
                  <input
                    v-model="searchForm.requestId"
                    type="text"
                    class="input w-full"
                    placeholder="输入请求ID"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">IP地址</label>
                  <input
                    v-model="searchForm.ipAddress"
                    type="text"
                    class="input w-full"
                    placeholder="输入IP地址，支持CIDR格式"
                  />
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">状态码</label>
                  <input
                    v-model="searchForm.statusCode"
                    type="text"
                    class="input w-full"
                    placeholder="输入状态码，如 200, 4xx, 5xx"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧：搜索预览和历史 -->
          <div class="space-y-6">
            <!-- 搜索语法预览 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">搜索语法预览</h3>
              <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ generatedQuery }}</pre>
              </div>
            </div>
            
            <!-- 搜索历史 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">搜索历史</h3>
              <div class="space-y-2 max-h-48 overflow-y-auto">
                <div
                  v-for="(history, index) in searchHistory"
                  :key="index"
                  class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600"
                  @click="loadSearchHistory(history)"
                >
                  <div class="flex-1 min-w-0">
                    <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {{ history.name || '未命名搜索' }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {{ formatDate(history.timestamp) }}
                    </div>
                  </div>
                  <button
                    @click.stop="removeSearchHistory(index)"
                    class="text-gray-400 hover:text-red-500"
                  >
                    <component :is="Trash2" class="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
            
            <!-- 快速搜索模板 -->
            <div>
              <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">快速模板</h3>
              <div class="grid grid-cols-1 gap-2">
                <button
                  v-for="template in searchTemplates"
                  :key="template.name"
                  @click="applyTemplate(template)"
                  class="text-left p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                >
                  <div class="text-sm font-medium text-blue-900 dark:text-blue-100">{{ template.name }}</div>
                  <div class="text-xs text-blue-600 dark:text-blue-300">{{ template.description }}</div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <button
            @click="saveSearch"
            class="btn-secondary"
          >
            <component :is="Save" class="w-4 h-4 mr-2" />
            保存搜索
          </button>
          <button
            @click="resetForm"
            class="btn-secondary"
          >
            <component :is="RotateCcw" class="w-4 h-4 mr-2" />
            重置
          </button>
        </div>
        
        <div class="flex items-center space-x-3">
          <button
            @click="$emit('close')"
            class="btn-secondary"
          >
            取消
          </button>
          <button
            @click="executeSearch"
            class="btn-primary"
          >
            <component :is="Search" class="w-4 h-4 mr-2" />
            执行搜索
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { X, Search, Save, RotateCcw, Trash2 } from 'lucide-vue-next'
import { format } from 'date-fns'
import { useAppStore } from '@/stores/appStore'

const emit = defineEmits(['close', 'search'])

const appStore = useAppStore()

// 搜索表单
const searchForm = ref({
  keywords: '',
  level: '',
  source: '',
  timeType: 'relative',
  relativeTime: '24h',
  startTime: '',
  endTime: '',
  userId: '',
  requestId: '',
  ipAddress: '',
  statusCode: ''
})

// 搜索历史
const searchHistory = ref([])

// 搜索模板
const searchTemplates = ref([
  {
    name: '错误日志分析',
    description: '查找最近24小时的错误日志',
    form: {
      keywords: '',
      level: 'error',
      source: '',
      timeType: 'relative',
      relativeTime: '24h'
    }
  },
  {
    name: 'API性能问题',
    description: '查找API响应时间超过1秒的请求',
    form: {
      keywords: 'response_time:[1000 TO *]',
      level: '',
      source: 'api',
      timeType: 'relative',
      relativeTime: '1h'
    }
  },
  {
    name: '用户行为追踪',
    description: '根据用户ID追踪用户行为',
    form: {
      keywords: '',
      level: '',
      source: '',
      timeType: 'relative',
      relativeTime: '24h',
      userId: ''
    }
  }
])

// 生成搜索查询语法
const generatedQuery = computed(() => {
  const conditions = []
  
  if (searchForm.value.keywords) {
    conditions.push(`message:"${searchForm.value.keywords}"`)
  }
  
  if (searchForm.value.level) {
    conditions.push(`level:${searchForm.value.level}`)
  }
  
  if (searchForm.value.source) {
    conditions.push(`source:${searchForm.value.source}`)
  }
  
  if (searchForm.value.userId) {
    conditions.push(`metadata.userId:"${searchForm.value.userId}"`)
  }
  
  if (searchForm.value.requestId) {
    conditions.push(`metadata.requestId:"${searchForm.value.requestId}"`)
  }
  
  if (searchForm.value.ipAddress) {
    conditions.push(`metadata.ip:"${searchForm.value.ipAddress}"`)
  }
  
  if (searchForm.value.statusCode) {
    conditions.push(`metadata.statusCode:${searchForm.value.statusCode}`)
  }
  
  // 时间条件
  if (searchForm.value.timeType === 'relative') {
    conditions.push(`timestamp:>now-${searchForm.value.relativeTime}`)
  } else if (searchForm.value.startTime || searchForm.value.endTime) {
    if (searchForm.value.startTime && searchForm.value.endTime) {
      conditions.push(`timestamp:[${searchForm.value.startTime} TO ${searchForm.value.endTime}]`)
    } else if (searchForm.value.startTime) {
      conditions.push(`timestamp:>${searchForm.value.startTime}`)
    } else if (searchForm.value.endTime) {
      conditions.push(`timestamp:<${searchForm.value.endTime}`)
    }
  }
  
  return conditions.length > 0 ? conditions.join(' AND ') : '(空查询)'
})

// 方法
const executeSearch = () => {
  const searchData = {
    ...searchForm.value,
    query: generatedQuery.value
  }
  
  // 保存到搜索历史
  addToSearchHistory(searchData)
  
  emit('search', searchData)
  emit('close')
}

const saveSearch = () => {
  const name = prompt('请输入搜索名称:')
  if (name) {
    const searchData = {
      name,
      form: { ...searchForm.value },
      query: generatedQuery.value,
      timestamp: new Date()
    }
    addToSearchHistory(searchData)
    
    appStore.addNotification({
      type: 'success',
      message: `搜索 "${name}" 已保存`
    })
  }
}

const resetForm = () => {
  searchForm.value = {
    keywords: '',
    level: '',
    source: '',
    timeType: 'relative',
    relativeTime: '24h',
    startTime: '',
    endTime: '',
    userId: '',
    requestId: '',
    ipAddress: '',
    statusCode: ''
  }
}

const applyTemplate = (template) => {
  searchForm.value = { ...searchForm.value, ...template.form }
}

const loadSearchHistory = (history) => {
  searchForm.value = { ...history.form }
}

const addToSearchHistory = (searchData) => {
  searchHistory.value.unshift(searchData)
  
  // 限制历史记录数量
  if (searchHistory.value.length > 20) {
    searchHistory.value = searchHistory.value.slice(0, 20)
  }
  
  // 保存到本地存储
  localStorage.setItem('logSearchHistory', JSON.stringify(searchHistory.value))
}

const removeSearchHistory = (index) => {
  searchHistory.value.splice(index, 1)
  localStorage.setItem('logSearchHistory', JSON.stringify(searchHistory.value))
}

const formatDate = (date) => {
  return format(new Date(date), 'MM-dd HH:mm')
}

// 生命周期
onMounted(() => {
  // 加载搜索历史
  const saved = localStorage.getItem('logSearchHistory')
  if (saved) {
    try {
      searchHistory.value = JSON.parse(saved)
    } catch (error) {
      console.error('Failed to load search history:', error)
    }
  }
})
</script>

<style scoped>
.form-radio {
  @apply h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700;
}
</style>
