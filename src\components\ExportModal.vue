<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="$emit('close')">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4" @click.stop>
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">导出日志</h2>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <component :is="X" class="w-6 h-6" />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6">
        <!-- 导出范围 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">导出范围</h3>
          <div class="space-y-2">
            <label class="flex items-center">
              <input
                v-model="exportScope"
                type="radio"
                value="filtered"
                class="form-radio text-blue-600"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                当前过滤结果 ({{ filteredCount }} 条)
              </span>
            </label>
            <label class="flex items-center">
              <input
                v-model="exportScope"
                type="radio"
                value="all"
                class="form-radio text-blue-600"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                全部日志 ({{ totalCount }} 条)
              </span>
            </label>
            <label class="flex items-center">
              <input
                v-model="exportScope"
                type="radio"
                value="selected"
                class="form-radio text-blue-600"
                :disabled="selectedCount === 0"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                选中的日志 ({{ selectedCount }} 条)
              </span>
            </label>
          </div>
        </div>
        
        <!-- 导出格式 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">导出格式</h3>
          <div class="grid grid-cols-2 gap-3">
            <label
              v-for="format in exportFormats"
              :key="format.value"
              class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
              :class="selectedFormats.includes(format.value) ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''"
            >
              <input
                v-model="selectedFormats"
                type="checkbox"
                :value="format.value"
                class="form-checkbox text-blue-600"
              />
              <div class="ml-3">
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ format.label }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ format.description }}</div>
              </div>
            </label>
          </div>
        </div>
        
        <!-- 高级选项 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">高级选项</h3>
          <div class="space-y-3">
            <label class="flex items-center">
              <input
                v-model="includeMetadata"
                type="checkbox"
                class="form-checkbox text-blue-600"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">包含元数据</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="includeStats"
                type="checkbox"
                class="form-checkbox text-blue-600"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">包含统计报告</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="compressOutput"
                type="checkbox"
                class="form-checkbox text-blue-600"
                :disabled="selectedFormats.length <= 1"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">压缩输出 (多格式时)</span>
            </label>
          </div>
        </div>
        
        <!-- 文件名 -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-900 dark:text-white mb-2">文件名前缀</label>
          <input
            v-model="filename"
            type="text"
            class="input w-full"
            placeholder="logs"
          />
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            实际文件名将包含时间戳和格式后缀
          </p>
        </div>
      </div>
      
      <!-- 底部按钮 -->
      <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
        <button
          @click="$emit('close')"
          class="btn-secondary"
        >
          取消
        </button>
        <button
          @click="handleExport"
          :disabled="selectedFormats.length === 0 || isExporting"
          class="btn-primary"
        >
          <component :is="isExporting ? RefreshCw : Download" :class="['w-4 h-4 mr-2', isExporting ? 'animate-spin' : '']" />
          {{ isExporting ? '导出中...' : '开始导出' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { X, Download, RefreshCw } from 'lucide-vue-next'
import { 
  exportLogsAsJSON, 
  exportLogsAsCSV, 
  exportLogsAsText, 
  exportLogsAsExcel,
  exportLogReport,
  exportLogsMultipleFormats
} from '@/utils/logExport'
import { useAppStore } from '@/stores/appStore'

const props = defineProps({
  logs: {
    type: Array,
    required: true
  },
  filteredLogs: {
    type: Array,
    required: true
  },
  selectedLogs: {
    type: Array,
    default: () => []
  },
  currentFilters: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close'])

const appStore = useAppStore()

// 导出配置
const exportScope = ref('filtered')
const selectedFormats = ref(['json'])
const includeMetadata = ref(true)
const includeStats = ref(false)
const compressOutput = ref(false)
const filename = ref('logs')
const isExporting = ref(false)

// 导出格式选项
const exportFormats = [
  {
    value: 'json',
    label: 'JSON',
    description: '结构化数据，便于程序处理'
  },
  {
    value: 'csv',
    label: 'CSV',
    description: '表格格式，Excel可打开'
  },
  {
    value: 'txt',
    label: '纯文本',
    description: '简单文本格式'
  },
  {
    value: 'excel',
    label: 'Excel',
    description: 'Excel兼容格式'
  }
]

// 计算属性
const totalCount = computed(() => props.logs.length)
const filteredCount = computed(() => props.filteredLogs.length)
const selectedCount = computed(() => props.selectedLogs.length)

// 获取要导出的日志
const getLogsToExport = () => {
  switch (exportScope.value) {
    case 'all':
      return props.logs
    case 'selected':
      return props.selectedLogs
    case 'filtered':
    default:
      return props.filteredLogs
  }
}

// 处理导出
const handleExport = async () => {
  if (selectedFormats.value.length === 0) {
    appStore.addNotification({
      type: 'warning',
      message: '请选择至少一种导出格式'
    })
    return
  }
  
  isExporting.value = true
  
  try {
    const logsToExport = getLogsToExport()
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    const baseFilename = `${filename.value}-${timestamp}`
    
    // 如果选择了多种格式且启用了压缩
    if (selectedFormats.value.length > 1 && compressOutput.value) {
      // 这里可以实现压缩导出
      appStore.addNotification({
        type: 'info',
        message: '压缩导出功能开发中，将分别导出各格式文件'
      })
    }
    
    // 导出各种格式
    for (const format of selectedFormats.value) {
      switch (format) {
        case 'json':
          exportLogsAsJSON(logsToExport, baseFilename)
          break
        case 'csv':
          exportLogsAsCSV(logsToExport, baseFilename)
          break
        case 'txt':
          exportLogsAsText(logsToExport, baseFilename)
          break
        case 'excel':
          exportLogsAsExcel(logsToExport, baseFilename)
          break
      }
    }
    
    // 如果包含统计报告
    if (includeStats.value) {
      exportLogReport(logsToExport, props.currentFilters, `${baseFilename}-report`)
    }
    
    appStore.addNotification({
      type: 'success',
      message: `成功导出 ${logsToExport.length} 条日志，共 ${selectedFormats.value.length} 种格式`
    })
    
    emit('close')
    
  } catch (error) {
    console.error('导出失败:', error)
    appStore.addNotification({
      type: 'error',
      message: '导出失败，请重试'
    })
  } finally {
    isExporting.value = false
  }
}
</script>

<style scoped>
.form-radio {
  @apply h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700;
}

.form-checkbox {
  @apply h-4 w-4 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700;
}
</style>
