<template>
  <div class="json-viewer h-full flex flex-col bg-gray-900 text-gray-300 font-mono">
    <!-- JSON查看器头部 -->
    <div class="json-header bg-gray-800 border-b border-gray-700 p-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <component :is="Code" class="w-4 h-4 text-gray-400" />
            <span class="text-sm font-medium text-gray-300">JSON视图</span>
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="togglePrettyPrint"
              class="p-1 text-gray-400 hover:text-gray-200 rounded"
              :class="{ 'text-green-400': prettyPrint }"
              title="格式化"
            >
              <component :is="AlignLeft" class="w-4 h-4" />
            </button>
            
            <button
              @click="toggleLineNumbers"
              class="p-1 text-gray-400 hover:text-gray-200 rounded"
              :class="{ 'text-green-400': showLineNumbers }"
              title="行号"
            >
              <component :is="Hash" class="w-4 h-4" />
            </button>
            
            <button
              @click="toggleWordWrap"
              class="p-1 text-gray-400 hover:text-gray-200 rounded"
              :class="{ 'text-green-400': wordWrap }"
              title="自动换行"
            >
              <component :is="WrapText" class="w-4 h-4" />
            </button>
            
            <button
              @click="collapseAll"
              class="p-1 text-gray-400 hover:text-gray-200 rounded"
              title="折叠所有"
            >
              <component :is="Minus" class="w-4 h-4" />
            </button>
            
            <button
              @click="expandAll"
              class="p-1 text-gray-400 hover:text-gray-200 rounded"
              title="展开所有"
            >
              <component :is="Plus" class="w-4 h-4" />
            </button>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            {{ logEntries.length }} 条记录
          </div>
          
          <button
            @click="copyAllJson"
            class="px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
          >
            <component :is="Copy" class="w-4 h-4 mr-1 inline" />
            复制JSON
          </button>
        </div>
      </div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-bar bg-gray-800 border-b border-gray-700 p-3">
      <div class="flex items-center space-x-3">
        <div class="flex-1 relative">
          <component :is="Search" class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索JSON内容..."
            class="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded text-gray-300 placeholder-gray-500 focus:border-green-500 focus:outline-none"
          />
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            @click="searchPrev"
            :disabled="searchResults.length === 0"
            class="p-2 text-gray-400 hover:text-gray-200 rounded disabled:opacity-50"
            title="上一个"
          >
            <component :is="ChevronUp" class="w-4 h-4" />
          </button>
          
          <button
            @click="searchNext"
            :disabled="searchResults.length === 0"
            class="p-2 text-gray-400 hover:text-gray-200 rounded disabled:opacity-50"
            title="下一个"
          >
            <component :is="ChevronDown" class="w-4 h-4" />
          </button>
          
          <span class="text-sm text-gray-500">
            {{ searchResults.length > 0 ? `${currentSearchIndex + 1}/${searchResults.length}` : '0/0' }}
          </span>
        </div>
      </div>
    </div>
    
    <!-- JSON内容区域 -->
    <div class="flex-1 overflow-hidden">
      <div 
        ref="jsonContent"
        class="h-full overflow-auto p-4"
        @scroll="handleScroll"
      >
        <div class="space-y-2">
          <div
            v-for="(entry, index) in visibleEntries"
            :key="entry.id"
            class="json-entry border border-gray-700 rounded-lg overflow-hidden"
            :class="{ 'border-green-500': selectedEntry === entry.id }"
          >
            <!-- 条目头部 -->
            <div 
              class="entry-header bg-gray-800 px-4 py-2 flex items-center justify-between cursor-pointer hover:bg-gray-750 transition-colors"
              @click="toggleEntry(entry)"
            >
              <div class="flex items-center space-x-3">
                <component 
                  :is="entry.expanded ? ChevronDown : ChevronRight" 
                  class="w-4 h-4 text-gray-400" 
                />
                
                <div class="flex items-center space-x-2">
                  <span class="text-sm text-gray-400">{{ formatTimestamp(entry.timestamp) }}</span>
                  <span 
                    class="px-2 py-1 text-xs rounded"
                    :class="getLevelClass(entry.level)"
                  >
                    {{ entry.level }}
                  </span>
                  <span class="text-sm text-gray-500">{{ entry.source }}</span>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <span class="text-xs text-gray-500">{{ getJsonSize(entry.data) }}</span>
                
                <button
                  @click.stop="copyEntry(entry)"
                  class="p-1 text-gray-400 hover:text-gray-200 rounded"
                  title="复制"
                >
                  <component :is="Copy" class="w-3 h-3" />
                </button>
                
                <button
                  @click.stop="selectEntry(entry)"
                  class="p-1 text-gray-400 hover:text-gray-200 rounded"
                  title="选择"
                >
                  <component :is="Check" class="w-3 h-3" />
                </button>
              </div>
            </div>
            
            <!-- JSON内容 -->
            <div v-if="entry.expanded" class="json-content bg-gray-900 p-4">
              <pre 
                class="text-sm overflow-x-auto"
                :class="{ 'whitespace-pre-wrap': wordWrap }"
                v-html="formatJson(entry.data, entry.id)"
              ></pre>
            </div>
          </div>
        </div>
        
        <!-- 加载更多 -->
        <div 
          v-if="hasMore"
          class="text-center py-4"
        >
          <button
            @click="loadMore"
            class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded transition-colors"
          >
            加载更多
          </button>
        </div>
      </div>
    </div>
    
    <!-- 状态栏 -->
    <div class="status-bar bg-gray-800 border-t border-gray-700 px-4 py-2 text-xs text-gray-500">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <span>总计: {{ totalEntries }} 条</span>
          <span>已显示: {{ visibleEntries.length }} 条</span>
          <span>已选择: {{ selectedEntries.length }} 条</span>
        </div>
        
        <div class="flex items-center space-x-4">
          <span>内存使用: {{ formatBytes(memoryUsage) }}</span>
          <span>渲染时间: {{ renderTime }}ms</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Code, 
  AlignLeft, 
  Hash, 
  WrapText, 
  Minus, 
  Plus, 
  Copy, 
  Search, 
  ChevronUp, 
  ChevronDown, 
  ChevronRight, 
  Check 
} from 'lucide-vue-next'

const appStore = useAppStore()

// 视图状态
const prettyPrint = ref(true)
const showLineNumbers = ref(true)
const wordWrap = ref(false)
const searchQuery = ref('')
const selectedEntry = ref(null)
const selectedEntries = ref([])

// 数据状态
const logEntries = ref([])
const visibleEntries = ref([])
const currentPage = ref(1)
const pageSize = 50
const hasMore = ref(true)

// 搜索状态
const searchResults = ref([])
const currentSearchIndex = ref(0)

// 性能监控
const memoryUsage = ref(0)
const renderTime = ref(0)
const jsonContent = ref(null)

const totalEntries = computed(() => logEntries.value.length)

// 生成模拟JSON日志数据
const generateJsonLogs = () => {
  const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']
  const sources = ['nginx', 'mysql', 'redis', 'api-server', 'auth-service']
  const users = ['admin', 'user1', 'user2', 'guest']
  const ips = ['*************', '*********', '***********', '************']
  
  const logs = []
  for (let i = 0; i < 200; i++) {
    const level = levels[Math.floor(Math.random() * levels.length)]
    const source = sources[Math.floor(Math.random() * sources.length)]
    
    // 生成复杂的JSON数据
    const data = {
      timestamp: new Date(Date.now() - Math.random() * 86400000 * 7).toISOString(),
      level: level,
      source: source,
      message: `Log message ${i + 1}`,
      metadata: {
        user: users[Math.floor(Math.random() * users.length)],
        ip: ips[Math.floor(Math.random() * ips.length)],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        sessionId: `session_${Math.random().toString(36).substr(2, 9)}`,
        requestId: `req_${Math.random().toString(36).substr(2, 12)}`
      },
      request: {
        method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
        url: `/api/v1/resource/${Math.floor(Math.random() * 1000)}`,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token_' + Math.random().toString(36).substr(2, 20),
          'X-Request-ID': `req_${Math.random().toString(36).substr(2, 12)}`
        },
        body: level === 'ERROR' ? { error: 'Something went wrong', code: 500 } : { status: 'success' }
      },
      response: {
        statusCode: level === 'ERROR' ? 500 : 200,
        responseTime: Math.floor(Math.random() * 2000) + 50,
        size: Math.floor(Math.random() * 10000) + 100
      },
      context: {
        environment: 'production',
        version: '1.2.3',
        buildId: 'build_' + Math.random().toString(36).substr(2, 8),
        tags: ['api', 'production', source]
      }
    }
    
    logs.push({
      id: i + 1,
      timestamp: new Date(data.timestamp),
      level: level,
      source: source,
      data: data,
      expanded: false
    })
  }
  
  logEntries.value = logs.sort((a, b) => b.timestamp - a.timestamp)
  loadMore()
}

const loadMore = () => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  const newEntries = logEntries.value.slice(start, end)
  
  visibleEntries.value.push(...newEntries)
  currentPage.value++
  
  hasMore.value = end < logEntries.value.length
  
  // 模拟内存使用计算
  memoryUsage.value = JSON.stringify(visibleEntries.value).length
}

const toggleEntry = (entry) => {
  entry.expanded = !entry.expanded
}

const selectEntry = (entry) => {
  selectedEntry.value = entry.id
  
  const index = selectedEntries.value.indexOf(entry.id)
  if (index > -1) {
    selectedEntries.value.splice(index, 1)
  } else {
    selectedEntries.value.push(entry.id)
  }
}

const copyEntry = (entry) => {
  const jsonText = JSON.stringify(entry.data, null, 2)
  navigator.clipboard.writeText(jsonText)
  appStore.addNotification({
    type: 'success',
    message: 'JSON已复制到剪贴板'
  })
}

const copyAllJson = () => {
  const allData = visibleEntries.value.map(entry => entry.data)
  const jsonText = JSON.stringify(allData, null, 2)
  navigator.clipboard.writeText(jsonText)
  appStore.addNotification({
    type: 'success',
    message: '所有JSON已复制到剪贴板'
  })
}

const formatJson = (data, entryId) => {
  const startTime = performance.now()
  
  let jsonString = prettyPrint.value 
    ? JSON.stringify(data, null, 2)
    : JSON.stringify(data)
  
  // 语法高亮
  jsonString = jsonString
    .replace(/(".*?"):/g, '<span class="text-blue-400">$1</span>:')
    .replace(/: (".*?")/g, ': <span class="text-green-400">$1</span>')
    .replace(/: (true|false)/g, ': <span class="text-purple-400">$1</span>')
    .replace(/: (null)/g, ': <span class="text-gray-500">$1</span>')
    .replace(/: (\d+)/g, ': <span class="text-yellow-400">$1</span>')
  
  // 搜索高亮
  if (searchQuery.value) {
    const regex = new RegExp(`(${escapeRegex(searchQuery.value)})`, 'gi')
    jsonString = jsonString.replace(regex, '<mark class="bg-yellow-400 text-black">$1</mark>')
  }
  
  // 行号
  if (showLineNumbers.value) {
    const lines = jsonString.split('\n')
    jsonString = lines.map((line, index) => {
      const lineNum = (index + 1).toString().padStart(3, ' ')
      return `<span class="text-gray-600 select-none mr-4">${lineNum}</span>${line}`
    }).join('\n')
  }
  
  renderTime.value = Math.round(performance.now() - startTime)
  
  return jsonString
}

const escapeRegex = (string) => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

const togglePrettyPrint = () => {
  prettyPrint.value = !prettyPrint.value
}

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const collapseAll = () => {
  visibleEntries.value.forEach(entry => {
    entry.expanded = false
  })
}

const expandAll = () => {
  visibleEntries.value.forEach(entry => {
    entry.expanded = true
  })
}

const searchPrev = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value - 1 + searchResults.value.length) % searchResults.value.length
    scrollToSearchResult()
  }
}

const searchNext = () => {
  if (searchResults.value.length > 0) {
    currentSearchIndex.value = (currentSearchIndex.value + 1) % searchResults.value.length
    scrollToSearchResult()
  }
}

const scrollToSearchResult = () => {
  // 实现滚动到搜索结果的逻辑
  appStore.addNotification({
    type: 'info',
    message: `跳转到搜索结果 ${currentSearchIndex.value + 1}`
  })
}

const handleScroll = () => {
  if (!jsonContent.value) return
  
  const { scrollTop, scrollHeight, clientHeight } = jsonContent.value
  
  // 接近底部时自动加载更多
  if (scrollTop + clientHeight >= scrollHeight - 100 && hasMore.value) {
    loadMore()
  }
}

const getLevelClass = (level) => {
  const classes = {
    ERROR: 'bg-red-600 text-white',
    WARN: 'bg-yellow-600 text-white',
    INFO: 'bg-blue-600 text-white',
    DEBUG: 'bg-gray-600 text-white'
  }
  return classes[level] || classes.INFO
}

const getJsonSize = (data) => {
  const size = JSON.stringify(data).length
  return formatBytes(size)
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTimestamp = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm:ss.SSS')
}

onMounted(() => {
  generateJsonLogs()
})
</script>

<style scoped>
.json-viewer {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.json-content pre {
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  font-size: 13px;
  line-height: 1.4;
}

.json-entry {
  transition: all 0.2s ease;
}

.json-entry:hover {
  border-color: #4b5563;
}

.entry-header:hover {
  background-color: #374151;
}

/* 自定义滚动条 */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #374151;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

mark {
  border-radius: 2px;
  padding: 1px 2px;
}
</style>
