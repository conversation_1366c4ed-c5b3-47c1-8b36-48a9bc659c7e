<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="$emit('close')">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4" @click.stop>
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">键盘快捷键</h2>
        <button
          @click="$emit('close')"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <component :is="X" class="w-6 h-6" />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 搜索和导航 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">搜索和导航</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">聚焦搜索框</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">K</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">显示/隐藏过滤器</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">F</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">清除搜索/关闭面板</span>
                <kbd class="kbd">Esc</kbd>
              </div>
            </div>
          </div>
          
          <!-- 操作 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">操作</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">刷新日志</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">R</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">导出日志</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">E</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">显示快捷键</span>
                <kbd class="kbd">?</kbd>
              </div>
            </div>
          </div>
          
          <!-- 快速过滤 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">快速过滤</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">仅错误</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">1</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">警告+错误</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">2</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">最近1小时</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">3</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">今天</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">4</kbd>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 视图 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">视图</h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">切换统计面板</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">S</kbd>
                </div>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600 dark:text-gray-400">表格/终端视图</span>
                <div class="flex items-center space-x-1">
                  <kbd class="kbd">Ctrl</kbd>
                  <span class="text-gray-400">+</span>
                  <kbd class="kbd">T</kbd>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 提示 -->
        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p class="text-sm text-blue-700 dark:text-blue-300">
            💡 提示：在 Mac 上使用 Cmd 键替代 Ctrl 键
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { X } from 'lucide-vue-next'

defineEmits(['close'])
</script>

<style scoped>
.kbd {
  @apply inline-flex items-center px-2 py-1 text-xs font-mono font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded dark:text-gray-300 dark:bg-gray-700 dark:border-gray-600;
  min-width: 1.5rem;
  justify-content: center;
}
</style>
