<template>
  <div class="log-alerts h-full flex flex-col">
    <!-- 头部 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">告警</h3>
      <button
        @click="showCreateAlertModal = true"
        class="btn-secondary text-sm"
      >
        <component :is="Plus" class="w-4 h-4" />
      </button>
    </div>

    <!-- 告警统计 -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <div class="grid grid-cols-2 gap-3">
        <div class="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded">
          <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ activeAlerts }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">活跃</div>
        </div>
        <div class="text-center p-2 bg-red-50 dark:bg-red-900/20 rounded">
          <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ triggeredToday }}</div>
          <div class="text-xs text-gray-500 dark:text-gray-400">今日触发</div>
        </div>
      </div>
    </div>
    
    <!-- 告警列表 -->
    <div class="flex-1 overflow-y-auto p-4">
      <div class="space-y-3">
        <div
          v-for="alert in alerts"
          :key="alert.id"
          class="alert-item p-3 rounded-lg border transition-all duration-200"
          :class="getAlertClass(alert.status)"
        >
          <!-- 告警头部 -->
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-2">
              <component
                :is="getAlertIcon(alert.severity)"
                class="w-4 h-4"
                :class="getAlertIconClass(alert.severity)"
              />
              <span
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                :class="getSeverityClass(alert.severity)"
              >
                {{ alert.severity.toUpperCase() }}
              </span>
              <span
                class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                :class="getStatusClass(alert.status)"
              >
                {{ getStatusText(alert.status) }}
              </span>
            </div>
            <div class="flex items-center space-x-1">
              <button
                @click="toggleAlert(alert)"
                :class="[
                  'p-1 rounded transition-colors',
                  alert.enabled
                    ? 'text-green-600 hover:text-green-800'
                    : 'text-gray-400 hover:text-gray-600'
                ]"
                :title="alert.enabled ? '禁用' : '启用'"
              >
                <component :is="alert.enabled ? ToggleRight : ToggleLeft" class="w-4 h-4" />
              </button>
              <button
                @click="editAlert(alert)"
                class="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 rounded"
                title="编辑"
              >
                <component :is="Edit" class="w-3 h-3" />
              </button>
              <button
                @click="deleteAlert(alert.id)"
                class="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 rounded"
                title="删除"
              >
                <component :is="Trash2" class="w-3 h-3" />
              </button>
            </div>
          </div>

          <!-- 告警标题 -->
          <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1 truncate">
            {{ alert.name }}
          </h4>

          <!-- 告警描述 -->
          <p class="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
            {{ alert.description }}
          </p>

          <!-- 告警条件 -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded p-2 mb-2">
            <div class="text-xs text-gray-600 dark:text-gray-400 font-mono truncate">
              {{ alert.condition }}
            </div>
          </div>

          <!-- 统计信息 -->
          <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <span>触发 {{ alert.triggerCount }} 次</span>
            <span>{{ formatTime(alert.lastTriggered) }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 创建告警模态框 -->
    <div v-if="showCreateAlertModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showCreateAlertModal = false">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto" @click.stop>
        <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">创建告警规则</h3>
          <button
            @click="showCreateAlertModal = false"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <component :is="X" class="w-6 h-6" />
          </button>
        </div>
        
        <div class="p-6 space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">告警名称</label>
              <input
                v-model="newAlert.name"
                type="text"
                class="input w-full"
                placeholder="输入告警名称"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">严重程度</label>
              <select v-model="newAlert.severity" class="input w-full">
                <option value="low">低</option>
                <option value="medium">中</option>
                <option value="high">高</option>
                <option value="critical">严重</option>
              </select>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">描述</label>
            <textarea
              v-model="newAlert.description"
              class="input w-full h-20"
              placeholder="输入告警描述"
            ></textarea>
          </div>
          
          <!-- 告警条件 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">告警条件</label>
            <div class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">字段</label>
                  <select v-model="newAlert.field" class="input w-full">
                    <option value="level">日志级别</option>
                    <option value="source">来源</option>
                    <option value="message">消息内容</option>
                    <option value="count">数量</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">操作符</label>
                  <select v-model="newAlert.operator" class="input w-full">
                    <option value="equals">等于</option>
                    <option value="contains">包含</option>
                    <option value="greater_than">大于</option>
                    <option value="less_than">小于</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">值</label>
                  <input
                    v-model="newAlert.value"
                    type="text"
                    class="input w-full"
                    placeholder="输入比较值"
                  />
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">时间窗口</label>
                  <select v-model="newAlert.timeWindow" class="input w-full">
                    <option value="1m">1分钟</option>
                    <option value="5m">5分钟</option>
                    <option value="15m">15分钟</option>
                    <option value="1h">1小时</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm text-gray-600 dark:text-gray-400 mb-1">阈值</label>
                  <input
                    v-model="newAlert.threshold"
                    type="number"
                    class="input w-full"
                    placeholder="触发阈值"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <!-- 通知设置 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">通知方式</label>
            <div class="space-y-2">
              <label class="flex items-center">
                <input
                  v-model="newAlert.notifications.email"
                  type="checkbox"
                  class="form-checkbox text-blue-600"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">邮件通知</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="newAlert.notifications.webhook"
                  type="checkbox"
                  class="form-checkbox text-blue-600"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Webhook通知</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="newAlert.notifications.browser"
                  type="checkbox"
                  class="form-checkbox text-blue-600"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">浏览器通知</span>
              </label>
            </div>
          </div>
        </div>
        
        <div class="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="showCreateAlertModal = false"
            class="btn-secondary"
          >
            取消
          </button>
          <button
            @click="saveAlert"
            class="btn-primary"
          >
            创建告警
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import {
  Plus,
  AlertTriangle,
  AlertCircle,
  Info,
  Zap,
  ToggleRight,
  ToggleLeft,
  Edit,
  Trash2,
  X
} from 'lucide-vue-next'
import { format } from 'date-fns'
import { useAppStore } from '@/stores/appStore'

const appStore = useAppStore()

// 状态
const showCreateAlertModal = ref(false)
const alerts = ref([])

// 新告警表单
const newAlert = ref({
  name: '',
  description: '',
  severity: 'medium',
  field: 'level',
  operator: 'equals',
  value: '',
  timeWindow: '5m',
  threshold: 1,
  notifications: {
    email: true,
    webhook: false,
    browser: true
  }
})

// 计算属性
const activeAlerts = computed(() => alerts.value.filter(alert => alert.enabled).length)
const triggeredToday = computed(() => {
  const today = new Date()
  today.setHours(0, 0, 0, 0)
  return alerts.value.filter(alert => new Date(alert.lastTriggered) >= today).length
})
const pendingAlerts = computed(() => alerts.value.filter(alert => alert.status === 'triggered').length)
const totalRules = computed(() => alerts.value.length)

// 方法
const getAlertClass = (status) => {
  const classes = {
    normal: 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800',
    triggered: 'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10',
    resolved: 'border-green-300 dark:border-green-700 bg-green-50 dark:bg-green-900/10'
  }
  return classes[status] || classes.normal
}

const getAlertIcon = (severity) => {
  const icons = {
    low: Info,
    medium: AlertCircle,
    high: AlertTriangle,
    critical: Zap
  }
  return icons[severity] || Info
}

const getAlertIconClass = (severity) => {
  const classes = {
    low: 'text-blue-500',
    medium: 'text-yellow-500',
    high: 'text-orange-500',
    critical: 'text-red-500'
  }
  return classes[severity] || 'text-blue-500'
}

const getSeverityClass = (severity) => {
  const classes = {
    low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[severity] || classes.medium
}

const getStatusClass = (status) => {
  const classes = {
    normal: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    triggered: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    resolved: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[status] || classes.normal
}

const getStatusText = (status) => {
  const texts = {
    normal: '正常',
    triggered: '已触发',
    resolved: '已解决'
  }
  return texts[status] || '未知'
}

const formatTime = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm')
}

const saveAlert = () => {
  const alert = {
    id: Date.now(),
    ...newAlert.value,
    enabled: true,
    status: 'normal',
    triggerCount: 0,
    lastTriggered: new Date(),
    createdAt: new Date(),
    condition: `${newAlert.value.field} ${newAlert.value.operator} ${newAlert.value.value} in ${newAlert.value.timeWindow}`
  }
  
  alerts.value.unshift(alert)
  saveAlertsToStorage()
  
  // 重置表单
  newAlert.value = {
    name: '',
    description: '',
    severity: 'medium',
    field: 'level',
    operator: 'equals',
    value: '',
    timeWindow: '5m',
    threshold: 1,
    notifications: {
      email: true,
      webhook: false,
      browser: true
    }
  }
  
  showCreateAlertModal.value = false
  
  appStore.addNotification({
    type: 'success',
    message: '告警规则已创建'
  })
}

const toggleAlert = (alert) => {
  alert.enabled = !alert.enabled
  saveAlertsToStorage()
  
  appStore.addNotification({
    type: 'info',
    message: `告警 "${alert.name}" 已${alert.enabled ? '启用' : '禁用'}`
  })
}

const editAlert = (alert) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑告警: ${alert.name}`
  })
}

const testAlert = (alert) => {
  appStore.addNotification({
    type: 'warning',
    message: `测试告警: ${alert.name}`
  })
}

const deleteAlert = (id) => {
  if (confirm('确定要删除这个告警规则吗？')) {
    alerts.value = alerts.value.filter(alert => alert.id !== id)
    saveAlertsToStorage()
    
    appStore.addNotification({
      type: 'success',
      message: '告警规则已删除'
    })
  }
}

const saveAlertsToStorage = () => {
  localStorage.setItem('logAlerts', JSON.stringify(alerts.value))
}

const loadAlertsFromStorage = () => {
  const saved = localStorage.getItem('logAlerts')
  if (saved) {
    try {
      alerts.value = JSON.parse(saved)
    } catch (error) {
      console.error('Failed to load alerts:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  loadAlertsFromStorage()
  
  // 生成示例告警
  if (alerts.value.length === 0) {
    alerts.value = [
      {
        id: 1,
        name: '错误日志激增',
        description: '当错误日志数量在5分钟内超过10条时触发',
        severity: 'high',
        field: 'level',
        operator: 'equals',
        value: 'error',
        timeWindow: '5m',
        threshold: 10,
        enabled: true,
        status: 'triggered',
        triggerCount: 5,
        lastTriggered: new Date(Date.now() - 300000),
        createdAt: new Date(Date.now() - 86400000),
        condition: 'level equals error in 5m',
        notifications: {
          email: true,
          webhook: true,
          browser: true
        }
      },
      {
        id: 2,
        name: '数据库连接异常',
        description: '检测数据库连接相关的错误',
        severity: 'critical',
        field: 'message',
        operator: 'contains',
        value: 'database connection',
        timeWindow: '1m',
        threshold: 1,
        enabled: true,
        status: 'normal',
        triggerCount: 2,
        lastTriggered: new Date(Date.now() - 3600000),
        createdAt: new Date(Date.now() - 172800000),
        condition: 'message contains database connection in 1m',
        notifications: {
          email: true,
          webhook: false,
          browser: true
        }
      }
    ]
  }
})
</script>

<style scoped>
.alert-item {
  transition: all 0.2s ease;
}

.alert-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.form-checkbox {
  @apply h-4 w-4 border-gray-300 rounded text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
