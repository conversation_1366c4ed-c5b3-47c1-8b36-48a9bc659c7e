<template>
  <div class="log-analytics h-full flex flex-col">
    <!-- 头部 -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">统计分析</h3>
      <button
        @click="refreshStats"
        :disabled="isLoading"
        class="btn-secondary text-sm"
      >
        <component :is="RefreshCw" :class="['w-4 h-4', isLoading ? 'animate-spin' : '']" />
      </button>
    </div>

    <!-- 内容 -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4">
      <!-- 核心指标 -->
      <div class="space-y-3">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <component :is="FileText" class="w-4 h-4 text-blue-600 dark:text-blue-400" />
              <span class="text-sm font-medium text-blue-700 dark:text-blue-300">总日志数</span>
            </div>
            <span class="text-lg font-bold text-blue-900 dark:text-blue-100">
              {{ formatNumber(stats.total) }}
            </span>
          </div>
          <div class="text-xs text-blue-600 dark:text-blue-400 mt-1">
            <component :is="TrendingUp" class="w-3 h-3 inline mr-1" />
            +{{ stats.todayIncrease }}% 今日
          </div>
        </div>

        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <component :is="AlertTriangle" class="w-4 h-4 text-red-600 dark:text-red-400" />
              <span class="text-sm font-medium text-red-700 dark:text-red-300">错误数量</span>
            </div>
            <span class="text-lg font-bold text-red-900 dark:text-red-100">
              {{ formatNumber(stats.errors) }}
            </span>
          </div>
          <div class="text-xs text-red-600 dark:text-red-400 mt-1">
            {{ stats.errorRate }}% 错误率
          </div>
        </div>

        <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-3">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <component :is="Clock" class="w-4 h-4 text-yellow-600 dark:text-yellow-400" />
              <span class="text-sm font-medium text-yellow-700 dark:text-yellow-300">响应时间</span>
            </div>
            <span class="text-lg font-bold text-yellow-900 dark:text-yellow-100">
              {{ stats.avgResponseTime }}ms
            </span>
          </div>
          <div class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
            {{ stats.responseTimeChange }}% vs 昨日
          </div>
        </div>
      </div>

      <!-- 日志级别分布 -->
      <div>
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">级别分布</h4>
        <div class="space-y-2">
          <div v-for="level in levelStats" :key="level.name" class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 rounded-full" :class="level.color"></div>
              <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">{{ level.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-1.5">
                <div
                  class="h-1.5 rounded-full transition-all duration-300"
                  :class="level.color"
                  :style="{ width: `${level.percentage}%` }"
                ></div>
              </div>
              <span class="text-sm font-medium text-gray-900 dark:text-white w-8 text-right">
                {{ level.count }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间趋势 -->
      <div>
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">24小时趋势</h4>
        <div class="h-20 flex items-end justify-between space-x-1">
          <div
            v-for="(bar, index) in timeSeriesData"
            :key="index"
            class="flex-1 bg-blue-200 dark:bg-blue-800 rounded-t transition-all duration-300 hover:bg-blue-300 dark:hover:bg-blue-700 cursor-pointer"
            :style="{ height: `${(bar.value / maxTimeSeriesValue) * 100}%` }"
            :title="`${bar.time}: ${bar.value} 条日志`"
          ></div>
        </div>
        <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
          <span>{{ timeSeriesData[0]?.time }}</span>
          <span>{{ timeSeriesData[timeSeriesData.length - 1]?.time }}</span>
        </div>
      </div>

      <!-- 热门来源 -->
      <div>
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">热门来源</h4>
        <div class="space-y-2">
          <div
            v-for="(source, index) in topSources.slice(0, 5)"
            :key="source.name"
            class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded"
          >
            <div class="flex items-center space-x-2">
              <div class="flex items-center justify-center w-5 h-5 bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 rounded text-xs font-medium">
                {{ index + 1 }}
              </div>
              <div class="min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ source.name }}</div>
                <div class="text-xs text-gray-500 truncate">{{ source.description }}</div>
              </div>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ formatNumber(source.count) }}</div>
              <div class="text-xs text-gray-500">{{ source.percentage }}%</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近错误 -->
      <div>
        <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3">最近错误</h4>
        <div class="space-y-2">
          <div
            v-for="error in topErrors.slice(0, 3)"
            :key="error.message"
            class="p-2 bg-red-50 dark:bg-red-900/10 rounded border border-red-200 dark:border-red-800"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-red-900 dark:text-red-200 truncate">{{ error.message }}</div>
                <div class="text-xs text-red-600 dark:text-red-400 mt-1">
                  {{ formatTime(error.lastOccurrence) }}
                </div>
              </div>
              <div class="text-right ml-2">
                <div class="text-sm font-medium text-red-900 dark:text-red-200">{{ error.count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { format } from 'date-fns'
import {
  FileText,
  AlertTriangle,
  Clock,
  TrendingUp,
  RefreshCw
} from 'lucide-vue-next'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'

const logStore = useLogStore()
const appStore = useAppStore()

const isLoading = ref(false)

// 统计数据
const stats = reactive({
  total: 0,
  errors: 0,
  errorRate: 0,
  avgResponseTime: 0,
  responseTimeChange: 0,
  todayIncrease: 0
})

// 数据
const levelStats = ref([])
const timeSeriesData = ref([])
const topSources = ref([])
const topErrors = ref([])

// 计算属性
const maxTimeSeriesValue = computed(() => {
  return Math.max(...timeSeriesData.value.map(d => d.value), 1)
})

// 方法
const formatNumber = (num) => {
  return new Intl.NumberFormat('zh-CN').format(num)
}

const formatTime = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm')
}

const refreshStats = async () => {
  isLoading.value = true
  try {
    await generateAnalytics()
    appStore.addNotification({
      type: 'success',
      message: '统计数据已更新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '统计数据更新失败'
    })
  } finally {
    isLoading.value = false
  }
}

const generateAnalytics = async () => {
  const logs = logStore.filteredLogs

  // 基础统计
  stats.total = logs.length
  stats.errors = logs.filter(log => log.level === 'error').length
  stats.errorRate = logs.length > 0 ? Math.round((stats.errors / logs.length) * 100) : 0
  stats.todayIncrease = Math.round(Math.random() * 20)
  stats.avgResponseTime = Math.round(Math.random() * 500) + 100
  stats.responseTimeChange = Math.round((Math.random() - 0.5) * 40)

  // 级别分布
  const levelCounts = {}
  logs.forEach(log => {
    levelCounts[log.level] = (levelCounts[log.level] || 0) + 1
  })

  levelStats.value = Object.entries(levelCounts).map(([level, count]) => ({
    name: level,
    count,
    percentage: Math.round((count / logs.length) * 100),
    color: getLevelColor(level)
  })).sort((a, b) => b.count - a.count)

  // 时间序列数据
  generateTimeSeriesData(logs)

  // 来源分析
  const sourceCounts = {}
  logs.forEach(log => {
    sourceCounts[log.source] = (sourceCounts[log.source] || 0) + 1
  })

  topSources.value = Object.entries(sourceCounts)
    .map(([name, count]) => ({
      name,
      description: getSourceDescription(name),
      count,
      percentage: Math.round((count / logs.length) * 100)
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)

  // 错误分析
  const errorLogs = logs.filter(log => log.level === 'error')
  topErrors.value = errorLogs
    .slice(0, 10)
    .map(log => ({
      message: log.message.substring(0, 50) + '...',
      count: Math.floor(Math.random() * 50) + 1,
      lastOccurrence: log.timestamp
    }))
}

const generateTimeSeriesData = (logs) => {
  const now = new Date()
  const hours = 24
  const data = []

  for (let i = hours - 1; i >= 0; i--) {
    const hour = new Date(now.getTime() - i * 60 * 60 * 1000)
    const hourStart = new Date(hour.getFullYear(), hour.getMonth(), hour.getDate(), hour.getHours())
    const hourEnd = new Date(hourStart.getTime() + 60 * 60 * 1000)

    const count = logs.filter(log => {
      const logTime = new Date(log.timestamp)
      return logTime >= hourStart && logTime < hourEnd
    }).length

    data.push({
      time: format(hourStart, 'HH:mm'),
      value: count
    })
  }

  timeSeriesData.value = data
}

const getLevelColor = (level) => {
  const colors = {
    error: 'bg-red-500',
    warn: 'bg-yellow-500',
    info: 'bg-blue-500',
    debug: 'bg-gray-500',
    trace: 'bg-purple-500'
  }
  return colors[level] || 'bg-gray-500'
}

const getSourceDescription = (source) => {
  const descriptions = {
    nginx: 'Web服务器',
    mysql: '数据库',
    redis: '缓存服务',
    'api-server': 'API服务',
    'auth-service': '认证服务',
    worker: '后台任务',
    scheduler: '定时任务'
  }
  return descriptions[source] || '系统服务'
}

onMounted(() => {
  generateAnalytics()
})
</script>
