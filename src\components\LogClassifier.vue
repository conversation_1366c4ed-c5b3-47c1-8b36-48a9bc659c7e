<template>
  <div class="space-y-6">
    <!-- 分类器概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">智能日志分类器</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="isTraining ? 'bg-yellow-500 animate-pulse' : 'bg-green-500'"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ isTraining ? '训练中' : '就绪' }}
            </span>
          </div>
          <button
            @click="trainClassifier"
            class="btn-primary"
            :disabled="isTraining"
          >
            <component :is="isTraining ? Loader : Brain" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isTraining }" />
            {{ isTraining ? '训练中...' : '训练模型' }}
          </button>
        </div>
      </div>
      
      <!-- 分类统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ classifierStats.totalLogs }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">已分类日志</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ classifierStats.accuracy }}%</div>
          <div class="text-sm text-green-600 dark:text-green-400">分类准确率</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ classifierStats.categories }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">分类类别</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ classifierStats.autoTagged }}</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">自动标签</div>
        </div>
      </div>
    </div>
    
    <!-- 分类规则管理 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 分类规则 -->
      <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white">分类规则</h4>
          <button
            @click="showAddRule = true"
            class="btn-primary"
          >
            <component :is="Plus" class="w-4 h-4 mr-2" />
            添加规则
          </button>
        </div>
        
        <div class="space-y-3">
          <div
            v-for="rule in classificationRules"
            :key="rule.id"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-3"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-3">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getCategoryClass(rule.category)"
                >
                  {{ rule.category }}
                </span>
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ rule.name }}</span>
              </div>
              <div class="flex items-center space-x-2">
                <button
                  @click="toggleRule(rule)"
                  class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
                >
                  <component :is="rule.enabled ? Pause : Play" class="w-3 h-3" />
                </button>
                <button
                  @click="editRule(rule)"
                  class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
                >
                  <component :is="Edit" class="w-3 h-3" />
                </button>
                <button
                  @click="deleteRule(rule)"
                  class="p-1 text-red-400 hover:text-red-600 rounded"
                >
                  <component :is="Trash2" class="w-3 h-3" />
                </button>
              </div>
            </div>
            <div class="text-xs text-gray-500">
              <span>条件: {{ rule.condition }}</span>
              <span class="ml-3">匹配: {{ rule.matchCount }} 次</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 标签管理 -->
      <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white">智能标签</h4>
          <button
            @click="showAddTag = true"
            class="btn-primary"
          >
            <component :is="Tag" class="w-4 h-4 mr-2" />
            添加标签
          </button>
        </div>
        
        <div class="space-y-3">
          <div
            v-for="tag in intelligentTags"
            :key="tag.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :style="{ backgroundColor: tag.color, color: getContrastColor(tag.color) }"
              >
                {{ tag.name }}
              </span>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ tag.description }}</div>
                <div class="text-xs text-gray-500">使用次数: {{ tag.usageCount }}</div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="editTag(tag)"
                class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="Edit" class="w-3 h-3" />
              </button>
              <button
                @click="deleteTag(tag)"
                class="p-1 text-red-400 hover:text-red-600 rounded"
              >
                <component :is="Trash2" class="w-3 h-3" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分类结果分析 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">分类结果分析</h4>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 分类分布 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">分类分布</h5>
          <div class="h-64">
            <ClassificationChart :data="classificationData" />
          </div>
        </div>
        
        <!-- 标签云 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">标签云</h5>
          <div class="h-64">
            <TagCloudChart :data="tagCloudData" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 实时分类监控 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">实时分类监控</h4>
      
      <div class="space-y-4">
        <div
          v-for="log in recentClassifiedLogs"
          :key="log.id"
          class="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="flex-1">
            <div class="flex items-center space-x-3 mb-2">
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getCategoryClass(log.category)"
              >
                {{ log.category }}
              </span>
              <span class="text-xs text-gray-500">{{ formatTime(log.timestamp) }}</span>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getConfidenceClass(log.confidence)"
              >
                置信度: {{ log.confidence }}%
              </span>
            </div>
            <div class="text-sm text-gray-900 dark:text-white mb-2">{{ log.message }}</div>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="tag in log.tags"
                :key="tag"
                class="px-2 py-1 text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded"
              >
                {{ tag }}
              </span>
            </div>
          </div>
          <div class="flex items-center space-x-2 ml-4">
            <button
              @click="correctClassification(log)"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              title="纠正分类"
            >
              <component :is="CheckCircle" class="w-4 h-4" />
            </button>
            <button
              @click="reportMisclassification(log)"
              class="p-2 text-red-400 hover:text-red-600 rounded"
              title="报告错误分类"
            >
              <component :is="XCircle" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 模型性能分析 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">模型性能分析</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 准确率趋势 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">准确率趋势</h5>
          <div class="h-32">
            <AccuracyTrendChart :data="accuracyTrendData" />
          </div>
        </div>
        
        <!-- 混淆矩阵 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">分类矩阵</h5>
          <div class="grid grid-cols-3 gap-1 text-xs">
            <div class="p-2 bg-gray-100 dark:bg-gray-800 text-center font-medium">实际\预测</div>
            <div class="p-2 bg-blue-100 dark:bg-blue-900 text-center">错误</div>
            <div class="p-2 bg-green-100 dark:bg-green-900 text-center">正常</div>
            <div class="p-2 bg-blue-100 dark:bg-blue-900 text-center">错误</div>
            <div class="p-2 bg-green-200 dark:bg-green-800 text-center font-bold">245</div>
            <div class="p-2 bg-red-200 dark:bg-red-800 text-center">12</div>
            <div class="p-2 bg-green-100 dark:bg-green-900 text-center">正常</div>
            <div class="p-2 bg-red-200 dark:bg-red-800 text-center">8</div>
            <div class="p-2 bg-green-200 dark:bg-green-800 text-center font-bold">1834</div>
          </div>
        </div>
        
        <!-- 性能指标 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">性能指标</h5>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">精确率</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">95.3%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">召回率</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">94.8%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">F1分数</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">95.0%</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">处理速度</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">1.2k/s</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Brain, 
  Loader, 
  Plus, 
  Tag, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle 
} from 'lucide-vue-next'

// 导入图表组件
import ClassificationChart from '@/components/charts/ClassificationChart.vue'
import TagCloudChart from '@/components/charts/TagCloudChart.vue'
import AccuracyTrendChart from '@/components/charts/AccuracyTrendChart.vue'

const appStore = useAppStore()

const isTraining = ref(false)
const showAddRule = ref(false)
const showAddTag = ref(false)

const classifierStats = reactive({
  totalLogs: 15420,
  accuracy: 94.5,
  categories: 12,
  autoTagged: 8934
})

const classificationRules = ref([
  {
    id: 1,
    name: '数据库错误分类',
    category: '数据库',
    condition: 'message contains "database" OR "connection" OR "timeout"',
    enabled: true,
    matchCount: 234
  },
  {
    id: 2,
    name: '安全事件分类',
    category: '安全',
    condition: 'message contains "failed login" OR "unauthorized" OR "blocked"',
    enabled: true,
    matchCount: 89
  },
  {
    id: 3,
    name: '性能问题分类',
    category: '性能',
    condition: 'response_time > 1000 OR message contains "slow"',
    enabled: false,
    matchCount: 156
  }
])

const intelligentTags = ref([
  {
    id: 1,
    name: '高优先级',
    description: '需要立即处理的问题',
    color: '#ef4444',
    usageCount: 234
  },
  {
    id: 2,
    name: '数据库相关',
    description: '与数据库操作相关的日志',
    color: '#3b82f6',
    usageCount: 567
  },
  {
    id: 3,
    name: '用户操作',
    description: '用户行为相关的日志',
    color: '#10b981',
    usageCount: 890
  },
  {
    id: 4,
    name: '系统维护',
    description: '系统维护和更新相关',
    color: '#f59e0b',
    usageCount: 123
  }
])

const recentClassifiedLogs = ref([
  {
    id: 1,
    message: '数据库连接超时，尝试重新连接',
    category: '数据库',
    confidence: 96,
    tags: ['高优先级', '数据库相关'],
    timestamp: new Date(Date.now() - 120000)
  },
  {
    id: 2,
    message: '用户登录失败，IP: *************',
    category: '安全',
    confidence: 89,
    tags: ['安全事件', '用户操作'],
    timestamp: new Date(Date.now() - 180000)
  },
  {
    id: 3,
    message: 'API响应时间超过阈值: 1.5秒',
    category: '性能',
    confidence: 92,
    tags: ['性能问题', '高优先级'],
    timestamp: new Date(Date.now() - 240000)
  }
])

const classificationData = ref([])
const tagCloudData = ref([])
const accuracyTrendData = ref([])

const getCategoryClass = (category) => {
  const classes = {
    '数据库': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    '安全': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    '性能': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    '用户': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    '系统': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  }
  return classes[category] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
}

const getConfidenceClass = (confidence) => {
  if (confidence >= 90) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  if (confidence >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const getContrastColor = (hexColor) => {
  // 简单的对比度计算
  const r = parseInt(hexColor.slice(1, 3), 16)
  const g = parseInt(hexColor.slice(3, 5), 16)
  const b = parseInt(hexColor.slice(5, 7), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}

const trainClassifier = async () => {
  isTraining.value = true
  
  try {
    // 模拟训练过程
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // 更新统计数据
    classifierStats.accuracy = Math.min(99, classifierStats.accuracy + Math.random() * 2)
    classifierStats.totalLogs += Math.floor(Math.random() * 1000) + 500
    
    appStore.addNotification({
      type: 'success',
      message: '模型训练完成，准确率提升至 ' + classifierStats.accuracy.toFixed(1) + '%'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '模型训练失败，请重试'
    })
  } finally {
    isTraining.value = false
  }
}

const toggleRule = (rule) => {
  rule.enabled = !rule.enabled
  appStore.addNotification({
    type: 'success',
    message: `分类规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`
  })
}

const editRule = (rule) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑分类规则: ${rule.name}`
  })
}

const deleteRule = (rule) => {
  if (confirm(`确定要删除分类规则 "${rule.name}" 吗？`)) {
    const index = classificationRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      classificationRules.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '分类规则已删除'
      })
    }
  }
}

const editTag = (tag) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑标签: ${tag.name}`
  })
}

const deleteTag = (tag) => {
  if (confirm(`确定要删除标签 "${tag.name}" 吗？`)) {
    const index = intelligentTags.value.findIndex(t => t.id === tag.id)
    if (index > -1) {
      intelligentTags.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '标签已删除'
      })
    }
  }
}

const correctClassification = (log) => {
  appStore.addNotification({
    type: 'success',
    message: '分类确认已记录，将用于模型优化'
  })
}

const reportMisclassification = (log) => {
  appStore.addNotification({
    type: 'warning',
    message: '错误分类已报告，将用于模型改进'
  })
}

const formatTime = (timestamp) => {
  return format(timestamp, 'HH:mm:ss')
}

// 初始化图表数据
classificationData.value = [
  { name: '数据库', value: 35 },
  { name: '安全', value: 25 },
  { name: '性能', value: 20 },
  { name: '用户', value: 15 },
  { name: '系统', value: 5 }
]

tagCloudData.value = [
  { text: '高优先级', size: 24, color: '#ef4444' },
  { text: '数据库相关', size: 20, color: '#3b82f6' },
  { text: '用户操作', size: 18, color: '#10b981' },
  { text: '系统维护', size: 16, color: '#f59e0b' },
  { text: '性能问题', size: 14, color: '#8b5cf6' },
  { text: '安全事件', size: 12, color: '#ef4444' }
]

accuracyTrendData.value = Array.from({ length: 24 }, (_, i) => ({
  time: new Date(Date.now() - (23 - i) * 3600000).toISOString(),
  value: 90 + Math.random() * 8
}))
</script>
