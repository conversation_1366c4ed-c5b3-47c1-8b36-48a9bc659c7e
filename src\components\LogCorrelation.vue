<template>
  <div class="space-y-6">
    <!-- 关联分析概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志关联分析</h3>
        <div class="flex items-center space-x-4">
          <select v-model="correlationTimeWindow" class="input w-32">
            <option value="5m">5分钟</option>
            <option value="15m">15分钟</option>
            <option value="1h">1小时</option>
            <option value="6h">6小时</option>
          </select>
          <button
            @click="runCorrelationAnalysis"
            class="btn-primary"
            :disabled="isAnalyzing"
          >
            <component :is="isAnalyzing ? Loader : GitBranch" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isAnalyzing }" />
            {{ isAnalyzing ? '分析中...' : '关联分析' }}
          </button>
        </div>
      </div>
      
      <!-- 关联统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ correlationStats.events }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">关联事件</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ correlationStats.chains }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">事件链</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ correlationStats.patterns }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">模式识别</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ correlationStats.confidence }}%</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">关联置信度</div>
        </div>
      </div>
    </div>
    
    <!-- 事件时间线 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">事件时间线</h4>
        <div class="flex items-center space-x-2">
          <button
            @click="zoomIn"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="放大"
          >
            <component :is="ZoomIn" class="w-4 h-4" />
          </button>
          <button
            @click="zoomOut"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="缩小"
          >
            <component :is="ZoomOut" class="w-4 h-4" />
          </button>
          <button
            @click="resetZoom"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="重置"
          >
            <component :is="RotateCcw" class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <div class="relative">
        <!-- 时间轴 -->
        <div class="flex items-center mb-6">
          <div class="flex-1 relative">
            <div class="h-2 bg-gray-200 dark:bg-gray-700 rounded-full">
              <div 
                class="h-2 bg-primary-500 rounded-full transition-all duration-300"
                :style="{ width: `${timelineProgress}%` }"
              ></div>
            </div>
            <!-- 时间标记 -->
            <div class="flex justify-between mt-2 text-xs text-gray-500">
              <span v-for="mark in timeMarks" :key="mark">{{ mark }}</span>
            </div>
          </div>
        </div>
        
        <!-- 事件节点 -->
        <div class="space-y-4">
          <div
            v-for="event in timelineEvents"
            :key="event.id"
            class="relative flex items-start space-x-4"
          >
            <!-- 连接线 -->
            <div class="absolute left-6 top-8 w-0.5 h-full bg-gray-200 dark:bg-gray-700"></div>
            
            <!-- 事件节点 -->
            <div 
              class="relative z-10 w-12 h-12 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-900"
              :class="getEventNodeClass(event.severity)"
            >
              <component :is="getEventIcon(event.type)" class="w-5 h-5 text-white" />
            </div>
            
            <!-- 事件内容 -->
            <div class="flex-1 min-w-0">
              <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                <div class="flex items-center justify-between mb-2">
                  <div class="flex items-center space-x-3">
                    <h5 class="font-medium text-gray-900 dark:text-white">{{ event.title }}</h5>
                    <span
                      class="px-2 py-1 text-xs font-medium rounded-full"
                      :class="getSeverityClass(event.severity)"
                    >
                      {{ event.severity }}
                    </span>
                  </div>
                  <span class="text-xs text-gray-500">{{ formatTime(event.timestamp) }}</span>
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ event.description }}</p>
                
                <!-- 关联事件 -->
                <div v-if="event.correlatedEvents.length > 0" class="mb-3">
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">关联事件:</div>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="correlated in event.correlatedEvents"
                      :key="correlated.id"
                      class="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded cursor-pointer hover:bg-blue-200 dark:hover:bg-blue-800"
                      @click="jumpToEvent(correlated.id)"
                    >
                      {{ correlated.title }}
                    </span>
                  </div>
                </div>
                
                <!-- 事件详情 -->
                <div class="grid grid-cols-2 gap-4 text-xs text-gray-500">
                  <div>
                    <span class="font-medium">来源:</span> {{ event.source }}
                  </div>
                  <div>
                    <span class="font-medium">服务:</span> {{ event.service }}
                  </div>
                  <div>
                    <span class="font-medium">用户:</span> {{ event.user || 'N/A' }}
                  </div>
                  <div>
                    <span class="font-medium">影响范围:</span> {{ event.impact }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 关联模式分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 关联规则 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">关联规则</h4>
        <div class="space-y-4">
          <div
            v-for="rule in correlationRules"
            :key="rule.id"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
          >
            <div class="flex items-center justify-between mb-2">
              <h5 class="font-medium text-gray-900 dark:text-white">{{ rule.name }}</h5>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getConfidenceClass(rule.confidence)"
              >
                {{ rule.confidence }}%
              </span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ rule.description }}</p>
            <div class="text-xs text-gray-500">
              <span>支持度: {{ rule.support }}%</span>
              <span class="ml-4">提升度: {{ rule.lift }}</span>
              <span class="ml-4">匹配次数: {{ rule.matchCount }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 关联网络图 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">关联网络</h4>
        <div class="h-64">
          <CorrelationNetworkChart :data="networkData" />
        </div>
      </div>
    </div>
    
    <!-- 异常事件序列 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">异常事件序列</h4>
      
      <div class="space-y-4">
        <div
          v-for="sequence in anomalySequences"
          :key="sequence.id"
          class="border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20 p-4 rounded-r-lg"
        >
          <div class="flex items-center justify-between mb-2">
            <h5 class="font-medium text-red-800 dark:text-red-200">{{ sequence.title }}</h5>
            <div class="flex items-center space-x-2">
              <span class="text-xs text-red-600 dark:text-red-400">异常分数: {{ sequence.anomalyScore }}</span>
              <button
                @click="investigateSequence(sequence)"
                class="text-xs text-red-600 hover:text-red-700 underline"
              >
                调查
              </button>
            </div>
          </div>
          <p class="text-sm text-red-700 dark:text-red-300 mb-3">{{ sequence.description }}</p>
          
          <!-- 事件序列 -->
          <div class="flex items-center space-x-2 overflow-x-auto">
            <div
              v-for="(step, index) in sequence.steps"
              :key="index"
              class="flex items-center space-x-2 flex-shrink-0"
            >
              <div class="px-3 py-1 bg-white dark:bg-gray-800 border border-red-200 dark:border-red-700 rounded text-xs">
                {{ step.event }}
              </div>
              <component 
                v-if="index < sequence.steps.length - 1"
                :is="ArrowRight" 
                class="w-4 h-4 text-red-500" 
              />
            </div>
          </div>
          
          <div class="mt-3 text-xs text-red-600 dark:text-red-400">
            <span>持续时间: {{ sequence.duration }}</span>
            <span class="ml-4">影响服务: {{ sequence.affectedServices.join(', ') }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  GitBranch, 
  Loader, 
  ZoomIn, 
  ZoomOut, 
  RotateCcw, 
  ArrowRight,
  AlertTriangle,
  Database,
  Shield,
  Zap,
  User
} from 'lucide-vue-next'

// 导入图表组件
import CorrelationNetworkChart from '@/components/charts/CorrelationNetworkChart.vue'

const appStore = useAppStore()

const isAnalyzing = ref(false)
const correlationTimeWindow = ref('1h')
const timelineProgress = ref(75)

const correlationStats = reactive({
  events: 156,
  chains: 23,
  patterns: 8,
  confidence: 87
})

const timeMarks = computed(() => {
  const now = new Date()
  const marks = []
  for (let i = 0; i < 5; i++) {
    const time = new Date(now.getTime() - (4 - i) * 3600000)
    marks.push(format(time, 'HH:mm'))
  }
  return marks
})

const timelineEvents = ref([
  {
    id: 1,
    title: '数据库连接异常',
    description: '数据库连接池耗尽，多个服务受到影响',
    type: 'database',
    severity: 'critical',
    timestamp: new Date(Date.now() - 3600000),
    source: 'mysql-server',
    service: 'user-service',
    user: null,
    impact: '高',
    correlatedEvents: [
      { id: 2, title: 'API响应超时' },
      { id: 3, title: '用户登录失败' }
    ]
  },
  {
    id: 2,
    title: 'API响应超时',
    description: '用户服务API响应时间超过5秒',
    type: 'performance',
    severity: 'high',
    timestamp: new Date(Date.now() - 3300000),
    source: 'api-gateway',
    service: 'user-service',
    user: null,
    impact: '中',
    correlatedEvents: [
      { id: 1, title: '数据库连接异常' }
    ]
  },
  {
    id: 3,
    title: '用户登录失败',
    description: '大量用户登录失败，可能与服务异常相关',
    type: 'security',
    severity: 'medium',
    timestamp: new Date(Date.now() - 3000000),
    source: 'auth-service',
    service: 'auth-service',
    user: 'multiple',
    impact: '中',
    correlatedEvents: [
      { id: 1, title: '数据库连接异常' },
      { id: 2, title: 'API响应超时' }
    ]
  },
  {
    id: 4,
    title: '系统恢复正常',
    description: '数据库连接池重新配置，服务恢复正常',
    type: 'system',
    severity: 'info',
    timestamp: new Date(Date.now() - 1800000),
    source: 'system',
    service: 'all',
    user: 'admin',
    impact: '低',
    correlatedEvents: []
  }
])

const correlationRules = ref([
  {
    id: 1,
    name: '数据库异常 → API超时',
    description: '数据库连接问题通常导致API响应超时',
    confidence: 94,
    support: 78,
    lift: 2.3,
    matchCount: 45
  },
  {
    id: 2,
    name: 'API超时 → 用户登录失败',
    description: 'API超时会导致用户认证服务异常',
    confidence: 87,
    support: 65,
    lift: 1.8,
    matchCount: 32
  },
  {
    id: 3,
    name: '内存不足 → 服务重启',
    description: '内存使用率过高通常导致服务自动重启',
    confidence: 91,
    support: 82,
    lift: 2.1,
    matchCount: 28
  }
])

const anomalySequences = ref([
  {
    id: 1,
    title: '级联故障序列',
    description: '数据库异常引发的级联故障，影响多个服务',
    anomalyScore: 0.92,
    duration: '45分钟',
    affectedServices: ['user-service', 'order-service', 'payment-service'],
    steps: [
      { event: '数据库连接超时' },
      { event: 'API响应延迟' },
      { event: '用户请求失败' },
      { event: '服务降级' },
      { event: '系统恢复' }
    ]
  },
  {
    id: 2,
    title: '安全攻击序列',
    description: '疑似协调攻击，多个IP同时发起攻击',
    anomalyScore: 0.85,
    duration: '20分钟',
    affectedServices: ['auth-service', 'api-gateway'],
    steps: [
      { event: '异常登录尝试' },
      { event: 'IP被标记' },
      { event: '攻击升级' },
      { event: '自动封禁' }
    ]
  }
])

const networkData = ref([])

const getEventNodeClass = (severity) => {
  const classes = {
    critical: 'bg-red-500',
    high: 'bg-orange-500',
    medium: 'bg-yellow-500',
    low: 'bg-blue-500',
    info: 'bg-green-500'
  }
  return classes[severity] || classes.info
}

const getEventIcon = (type) => {
  const icons = {
    database: Database,
    performance: Zap,
    security: Shield,
    system: AlertTriangle,
    user: User
  }
  return icons[type] || AlertTriangle
}

const getSeverityClass = (severity) => {
  const classes = {
    critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    low: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    info: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[severity] || classes.info
}

const getConfidenceClass = (confidence) => {
  if (confidence >= 90) return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  if (confidence >= 70) return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
}

const runCorrelationAnalysis = async () => {
  isAnalyzing.value = true
  
  try {
    // 模拟关联分析
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新统计数据
    correlationStats.events = Math.floor(Math.random() * 100) + 100
    correlationStats.chains = Math.floor(Math.random() * 20) + 15
    correlationStats.patterns = Math.floor(Math.random() * 10) + 5
    correlationStats.confidence = Math.floor(Math.random() * 20) + 80
    
    appStore.addNotification({
      type: 'success',
      message: `关联分析完成，发现 ${correlationStats.chains} 个事件链`
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '关联分析失败，请重试'
    })
  } finally {
    isAnalyzing.value = false
  }
}

const zoomIn = () => {
  appStore.addNotification({
    type: 'info',
    message: '时间线放大'
  })
}

const zoomOut = () => {
  appStore.addNotification({
    type: 'info',
    message: '时间线缩小'
  })
}

const resetZoom = () => {
  appStore.addNotification({
    type: 'info',
    message: '时间线重置'
  })
}

const jumpToEvent = (eventId) => {
  appStore.addNotification({
    type: 'info',
    message: `跳转到事件 ${eventId}`
  })
}

const investigateSequence = (sequence) => {
  appStore.addNotification({
    type: 'info',
    message: `开始调查异常序列: ${sequence.title}`
  })
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm:ss')
}

// 初始化网络图数据
networkData.value = {
  nodes: [
    { id: 'db', label: '数据库', type: 'database' },
    { id: 'api', label: 'API网关', type: 'service' },
    { id: 'auth', label: '认证服务', type: 'service' },
    { id: 'user', label: '用户服务', type: 'service' }
  ],
  links: [
    { source: 'db', target: 'api', strength: 0.9 },
    { source: 'api', target: 'auth', strength: 0.7 },
    { source: 'api', target: 'user', strength: 0.8 },
    { source: 'auth', target: 'user', strength: 0.6 }
  ]
}
</script>
