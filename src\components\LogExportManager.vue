<template>
  <div class="log-export-manager space-y-6">
    <!-- 导出任务创建 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">创建导出任务</h3>
        <button
          @click="showQuickExport = !showQuickExport"
          class="btn-secondary"
        >
          <component :is="Zap" class="w-4 h-4 mr-2" />
          快速导出
        </button>
      </div>
      
      <!-- 快速导出面板 -->
      <div v-if="showQuickExport" class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <h4 class="text-md font-medium text-blue-900 dark:text-blue-200 mb-3">快速导出选项</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            v-for="preset in quickExportPresets"
            :key="preset.key"
            @click="applyQuickExport(preset)"
            class="p-3 bg-white dark:bg-gray-800 border border-blue-200 dark:border-blue-700 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors text-left"
          >
            <div class="font-medium text-sm text-gray-900 dark:text-white">{{ preset.name }}</div>
            <div class="text-xs text-gray-600 dark:text-gray-400 mt-1">{{ preset.description }}</div>
          </button>
        </div>
      </div>
      
      <!-- 导出配置表单 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 基本设置 -->
        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900 dark:text-white">基本设置</h4>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              任务名称
            </label>
            <input
              v-model="exportConfig.name"
              type="text"
              placeholder="输入任务名称..."
              class="input"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              导出格式
            </label>
            <select v-model="exportConfig.format" class="input">
              <option value="json">JSON</option>
              <option value="csv">CSV</option>
              <option value="txt">纯文本</option>
              <option value="xlsx">Excel</option>
              <option value="parquet">Parquet</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              压缩方式
            </label>
            <select v-model="exportConfig.compression" class="input">
              <option value="none">无压缩</option>
              <option value="gzip">GZIP</option>
              <option value="zip">ZIP</option>
              <option value="bz2">BZIP2</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              分割大小 (MB)
            </label>
            <input
              v-model="exportConfig.splitSize"
              type="number"
              min="1"
              max="1000"
              placeholder="100"
              class="input"
            />
            <div class="text-xs text-gray-500 mt-1">
              超过此大小将自动分割为多个文件
            </div>
          </div>
        </div>
        
        <!-- 过滤条件 -->
        <div class="space-y-4">
          <h4 class="text-md font-medium text-gray-900 dark:text-white">过滤条件</h4>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              时间范围
            </label>
            <div class="grid grid-cols-2 gap-2">
              <input
                v-model="exportConfig.startTime"
                type="datetime-local"
                class="input"
              />
              <input
                v-model="exportConfig.endTime"
                type="datetime-local"
                class="input"
              />
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              日志级别
            </label>
            <div class="space-y-2">
              <label
                v-for="level in logLevels"
                :key="level"
                class="flex items-center space-x-2"
              >
                <input
                  type="checkbox"
                  :value="level"
                  v-model="exportConfig.levels"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ level }}</span>
              </label>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              日志来源
            </label>
            <div class="space-y-2 max-h-32 overflow-y-auto">
              <label
                v-for="source in logSources"
                :key="source"
                class="flex items-center space-x-2"
              >
                <input
                  type="checkbox"
                  :value="source"
                  v-model="exportConfig.sources"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">{{ source }}</span>
              </label>
            </div>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              关键词过滤
            </label>
            <input
              v-model="exportConfig.keyword"
              type="text"
              placeholder="输入关键词..."
              class="input"
            />
          </div>
        </div>
      </div>
      
      <!-- 高级选项 -->
      <div class="mt-6">
        <button
          @click="showAdvancedOptions = !showAdvancedOptions"
          class="flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
        >
          <component :is="showAdvancedOptions ? ChevronDown : ChevronRight" class="w-4 h-4 mr-1" />
          高级选项
        </button>
        
        <div v-if="showAdvancedOptions" class="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                字段选择
              </label>
              <div class="space-y-2 max-h-32 overflow-y-auto">
                <label
                  v-for="field in availableFields"
                  :key="field"
                  class="flex items-center space-x-2"
                >
                  <input
                    type="checkbox"
                    :value="field"
                    v-model="exportConfig.fields"
                    class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span class="text-sm text-gray-700 dark:text-gray-300">{{ field }}</span>
                </label>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                排序方式
              </label>
              <select v-model="exportConfig.sortBy" class="input mb-2">
                <option value="timestamp">时间戳</option>
                <option value="level">日志级别</option>
                <option value="source">来源</option>
              </select>
              <select v-model="exportConfig.sortOrder" class="input">
                <option value="asc">升序</option>
                <option value="desc">降序</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                最大记录数
              </label>
              <input
                v-model="exportConfig.maxRecords"
                type="number"
                min="1"
                placeholder="不限制"
                class="input"
              />
            </div>
            
            <div class="flex items-center space-x-4">
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="exportConfig.includeMetadata"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">包含元数据</span>
              </label>
              
              <label class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="exportConfig.anonymize"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-700 dark:text-gray-300">匿名化处理</span>
              </label>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="flex justify-between items-center mt-6">
        <div class="flex space-x-2">
          <button
            @click="saveTemplate"
            class="btn-secondary"
          >
            <component :is="Save" class="w-4 h-4 mr-2" />
            保存模板
          </button>
          <button
            @click="loadTemplate"
            class="btn-secondary"
          >
            <component :is="FolderOpen" class="w-4 h-4 mr-2" />
            加载模板
          </button>
        </div>
        
        <div class="flex space-x-2">
          <button
            @click="previewExport"
            class="btn-secondary"
          >
            <component :is="Eye" class="w-4 h-4 mr-2" />
            预览
          </button>
          <button
            @click="startExport"
            class="btn-primary"
            :disabled="isExporting"
          >
            <component :is="isExporting ? Loader : Download" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isExporting }" />
            {{ isExporting ? '导出中...' : '开始导出' }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- 导出任务列表 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">导出任务</h3>
        <div class="flex items-center space-x-2">
          <button
            @click="refreshTasks"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="刷新"
          >
            <component :is="RefreshCw" class="w-4 h-4" />
          </button>
          <select v-model="taskFilter" class="input w-32">
            <option value="">全部状态</option>
            <option value="pending">等待中</option>
            <option value="running">进行中</option>
            <option value="completed">已完成</option>
            <option value="failed">已失败</option>
          </select>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                任务名称
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                进度
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                文件大小
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="task in filteredTasks" :key="task.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <component :is="getTaskIcon(task.status)" class="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ task.name }}</div>
                    <div class="text-sm text-gray-500">{{ task.format.toUpperCase() }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusClass(task.status)"
                >
                  {{ getStatusLabel(task.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div 
                    class="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    :style="{ width: `${task.progress}%` }"
                  ></div>
                </div>
                <div class="text-xs text-gray-500 mt-1">{{ task.progress }}%</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ formatFileSize(task.fileSize) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatTime(task.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    v-if="task.status === 'completed'"
                    @click="downloadTask(task)"
                    class="text-green-600 hover:text-green-900"
                    title="下载"
                  >
                    <component :is="Download" class="w-4 h-4" />
                  </button>
                  <button
                    v-if="task.status === 'running'"
                    @click="pauseTask(task)"
                    class="text-yellow-600 hover:text-yellow-900"
                    title="暂停"
                  >
                    <component :is="Pause" class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteTask(task)"
                    class="text-red-600 hover:text-red-900"
                    title="删除"
                  >
                    <component :is="Trash2" class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Zap, 
  ChevronDown, 
  ChevronRight, 
  Save, 
  FolderOpen, 
  Eye, 
  Download, 
  Loader, 
  RefreshCw, 
  Trash2, 
  Pause,
  FileText,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-vue-next'

const appStore = useAppStore()

// 状态
const showQuickExport = ref(false)
const showAdvancedOptions = ref(false)
const isExporting = ref(false)
const taskFilter = ref('')

// 导出配置
const exportConfig = reactive({
  name: '',
  format: 'json',
  compression: 'gzip',
  splitSize: 100,
  startTime: '',
  endTime: '',
  levels: ['INFO', 'WARNING', 'ERROR'],
  sources: [],
  keyword: '',
  fields: ['timestamp', 'level', 'source', 'message'],
  sortBy: 'timestamp',
  sortOrder: 'desc',
  maxRecords: '',
  includeMetadata: true,
  anonymize: false
})

// 数据
const logLevels = ['TRACE', 'DEBUG', 'INFO', 'WARNING', 'ERROR', 'FATAL']
const logSources = ['nginx', 'mysql', 'redis', 'api-server', 'auth-service', 'payment-service']
const availableFields = ['timestamp', 'level', 'source', 'message', 'ip', 'user', 'responseTime', 'statusCode']

const quickExportPresets = [
  {
    key: 'errors-today',
    name: '今日错误',
    description: '今天的所有错误日志',
    config: { levels: ['ERROR'], startTime: new Date().toISOString().split('T')[0] + 'T00:00' }
  },
  {
    key: 'last-hour',
    name: '最近1小时',
    description: '最近1小时的所有日志',
    config: { startTime: new Date(Date.now() - 3600000).toISOString().slice(0, 16) }
  },
  {
    key: 'nginx-access',
    name: 'Nginx访问',
    description: 'Nginx访问日志',
    config: { sources: ['nginx'], levels: ['INFO'] }
  },
  {
    key: 'full-backup',
    name: '完整备份',
    description: '所有日志的完整备份',
    config: { format: 'parquet', compression: 'gzip' }
  }
]

const exportTasks = ref([
  {
    id: 1,
    name: '今日错误日志导出',
    status: 'completed',
    progress: 100,
    format: 'json',
    fileSize: 15728640,
    createdAt: new Date(Date.now() - 3600000)
  },
  {
    id: 2,
    name: 'Nginx访问日志',
    status: 'running',
    progress: 65,
    format: 'csv',
    fileSize: 52428800,
    createdAt: new Date(Date.now() - 1800000)
  },
  {
    id: 3,
    name: '完整备份',
    status: 'pending',
    progress: 0,
    format: 'parquet',
    fileSize: 0,
    createdAt: new Date(Date.now() - 900000)
  }
])

const filteredTasks = computed(() => {
  if (!taskFilter.value) return exportTasks.value
  return exportTasks.value.filter(task => task.status === taskFilter.value)
})

const applyQuickExport = (preset) => {
  Object.assign(exportConfig, preset.config)
  exportConfig.name = preset.name
  showQuickExport.value = false
  
  appStore.addNotification({
    type: 'success',
    message: `已应用快速导出模板: ${preset.name}`
  })
}

const startExport = async () => {
  if (!exportConfig.name) {
    appStore.addNotification({
      type: 'error',
      message: '请输入任务名称'
    })
    return
  }
  
  isExporting.value = true
  
  try {
    // 模拟导出过程
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 添加新任务
    const newTask = {
      id: Date.now(),
      name: exportConfig.name,
      status: 'running',
      progress: 0,
      format: exportConfig.format,
      fileSize: 0,
      createdAt: new Date()
    }
    
    exportTasks.value.unshift(newTask)
    
    // 模拟进度更新
    const progressInterval = setInterval(() => {
      newTask.progress += Math.random() * 20
      newTask.fileSize += Math.random() * 1000000
      
      if (newTask.progress >= 100) {
        newTask.progress = 100
        newTask.status = 'completed'
        clearInterval(progressInterval)
      }
    }, 1000)
    
    appStore.addNotification({
      type: 'success',
      message: '导出任务已创建'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '创建导出任务失败'
    })
  } finally {
    isExporting.value = false
  }
}

const previewExport = () => {
  appStore.addNotification({
    type: 'info',
    message: '预览功能开发中...'
  })
}

const saveTemplate = () => {
  appStore.addNotification({
    type: 'success',
    message: '导出模板已保存'
  })
}

const loadTemplate = () => {
  appStore.addNotification({
    type: 'info',
    message: '加载导出模板'
  })
}

const refreshTasks = () => {
  appStore.addNotification({
    type: 'success',
    message: '任务列表已刷新'
  })
}

const downloadTask = (task) => {
  appStore.addNotification({
    type: 'success',
    message: `开始下载: ${task.name}`
  })
}

const pauseTask = (task) => {
  task.status = 'paused'
  appStore.addNotification({
    type: 'info',
    message: `任务已暂停: ${task.name}`
  })
}

const deleteTask = (task) => {
  const index = exportTasks.value.findIndex(t => t.id === task.id)
  if (index > -1) {
    exportTasks.value.splice(index, 1)
    appStore.addNotification({
      type: 'success',
      message: '任务已删除'
    })
  }
}

const getTaskIcon = (status) => {
  const icons = {
    pending: Clock,
    running: Loader,
    completed: CheckCircle,
    failed: XCircle,
    paused: Pause
  }
  return icons[status] || FileText
}

const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    running: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    failed: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    paused: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
  return classes[status] || classes.pending
}

const getStatusLabel = (status) => {
  const labels = {
    pending: '等待中',
    running: '进行中',
    completed: '已完成',
    failed: '已失败',
    paused: '已暂停'
  }
  return labels[status] || '未知'
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}
</script>
