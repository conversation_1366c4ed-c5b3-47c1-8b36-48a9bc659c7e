<template>
  <div class="space-y-6">
    <!-- 索引管理概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志索引管理</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="indexStatus.health === 'green' ? 'bg-green-500' : 
                      indexStatus.health === 'yellow' ? 'bg-yellow-500' : 'bg-red-500'"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ getHealthText(indexStatus.health) }}
            </span>
          </div>
          <button
            @click="optimizeIndices"
            class="btn-primary"
            :disabled="isOptimizing"
          >
            <component :is="isOptimizing ? Loader : Zap" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isOptimizing }" />
            {{ isOptimizing ? '优化中...' : '优化索引' }}
          </button>
        </div>
      </div>
      
      <!-- 索引统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ indexStats.totalIndices }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">总索引数</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ indexStats.totalSize }}GB</div>
          <div class="text-sm text-green-600 dark:text-green-400">索引大小</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ indexStats.searchSpeed }}ms</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">平均搜索时间</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ indexStats.efficiency }}%</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">索引效率</div>
        </div>
      </div>
    </div>
    
    <!-- 索引列表 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">索引列表</h4>
        <div class="flex items-center space-x-4">
          <select v-model="filterStatus" class="input w-32">
            <option value="">全部状态</option>
            <option value="active">活跃</option>
            <option value="inactive">非活跃</option>
            <option value="optimizing">优化中</option>
          </select>
          <button
            @click="showCreateIndex = true"
            class="btn-primary"
          >
            <component :is="Plus" class="w-4 h-4 mr-2" />
            创建索引
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                索引名称
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                文档数量
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                大小
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                最后更新
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="index in filteredIndices" :key="index.name">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <component :is="Database" class="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ index.name }}</div>
                    <div class="text-sm text-gray-500">{{ index.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusClass(index.status)"
                >
                  {{ getStatusLabel(index.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ index.documentCount.toLocaleString() }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ index.size }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatTime(index.lastUpdated) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex items-center space-x-2">
                  <button
                    @click="optimizeIndex(index)"
                    class="text-blue-600 hover:text-blue-900"
                    title="优化索引"
                  >
                    <component :is="Zap" class="w-4 h-4" />
                  </button>
                  <button
                    @click="reindexData(index)"
                    class="text-green-600 hover:text-green-900"
                    title="重建索引"
                  >
                    <component :is="RefreshCw" class="w-4 h-4" />
                  </button>
                  <button
                    @click="viewIndexDetails(index)"
                    class="text-gray-600 hover:text-gray-900"
                    title="查看详情"
                  >
                    <component :is="Eye" class="w-4 h-4" />
                  </button>
                  <button
                    @click="deleteIndex(index)"
                    class="text-red-600 hover:text-red-900"
                    title="删除索引"
                  >
                    <component :is="Trash2" class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- 搜索性能分析 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 搜索性能趋势 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">搜索性能趋势</h4>
        <div class="h-64">
          <SearchPerformanceChart :data="searchPerformanceData" />
        </div>
      </div>
      
      <!-- 热门搜索词 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">热门搜索词</h4>
        <div class="space-y-3">
          <div
            v-for="term in popularSearchTerms"
            :key="term.term"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ term.term }}</span>
              <span class="text-xs text-gray-500">{{ term.category }}</span>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ term.count }}</div>
              <div class="text-xs text-gray-500">{{ term.avgTime }}ms</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 索引配置 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">索引配置</h4>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 自动优化设置 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">自动优化设置</h5>
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-700 dark:text-gray-300">启用自动优化</span>
              <button
                @click="toggleAutoOptimization"
                class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
                :class="autoOptimization.enabled ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
              >
                <span
                  class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                  :class="autoOptimization.enabled ? 'translate-x-5' : 'translate-x-0'"
                ></span>
              </button>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                优化频率
              </label>
              <select v-model="autoOptimization.frequency" class="input">
                <option value="daily">每日</option>
                <option value="weekly">每周</option>
                <option value="monthly">每月</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                优化阈值: {{ autoOptimization.threshold }}%
              </label>
              <input
                type="range"
                v-model="autoOptimization.threshold"
                min="50"
                max="90"
                class="w-full"
              />
              <div class="text-xs text-gray-500 mt-1">
                当索引效率低于此值时自动优化
              </div>
            </div>
          </div>
        </div>
        
        <!-- 存储策略 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">存储策略</h5>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                数据保留期
              </label>
              <select v-model="storageStrategy.retentionPeriod" class="input">
                <option value="30d">30天</option>
                <option value="90d">90天</option>
                <option value="1y">1年</option>
                <option value="forever">永久</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                压缩策略
              </label>
              <select v-model="storageStrategy.compression" class="input">
                <option value="none">无压缩</option>
                <option value="lz4">LZ4 (快速)</option>
                <option value="gzip">GZIP (平衡)</option>
                <option value="lzma">LZMA (高压缩)</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                分片策略
              </label>
              <select v-model="storageStrategy.sharding" class="input">
                <option value="time">按时间分片</option>
                <option value="size">按大小分片</option>
                <option value="hash">哈希分片</option>
              </select>
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end mt-6">
        <button
          @click="saveIndexConfig"
          class="btn-primary"
        >
          <component :is="Save" class="w-4 h-4 mr-2" />
          保存配置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Zap, 
  Loader, 
  Plus, 
  Database, 
  RefreshCw, 
  Eye, 
  Trash2, 
  Save 
} from 'lucide-vue-next'

// 导入图表组件
import SearchPerformanceChart from '@/components/charts/SearchPerformanceChart.vue'

const appStore = useAppStore()

const isOptimizing = ref(false)
const showCreateIndex = ref(false)
const filterStatus = ref('')

const indexStatus = reactive({
  health: 'green'
})

const indexStats = reactive({
  totalIndices: 12,
  totalSize: 45.6,
  searchSpeed: 125,
  efficiency: 87
})

const indices = ref([
  {
    name: 'logs-2024-01',
    description: '2024年1月日志索引',
    status: 'active',
    documentCount: 1250000,
    size: '8.5GB',
    lastUpdated: new Date(Date.now() - 3600000)
  },
  {
    name: 'logs-2024-02',
    description: '2024年2月日志索引',
    status: 'active',
    documentCount: 980000,
    size: '6.2GB',
    lastUpdated: new Date(Date.now() - 7200000)
  },
  {
    name: 'logs-error',
    description: '错误日志专用索引',
    status: 'optimizing',
    documentCount: 45000,
    size: '2.1GB',
    lastUpdated: new Date(Date.now() - 1800000)
  },
  {
    name: 'logs-archive',
    description: '归档日志索引',
    status: 'inactive',
    documentCount: 5600000,
    size: '28.8GB',
    lastUpdated: new Date(Date.now() - 86400000)
  }
])

const popularSearchTerms = ref([
  { term: 'error', category: '错误', count: 1250, avgTime: 45 },
  { term: 'timeout', category: '性能', count: 890, avgTime: 67 },
  { term: 'database', category: '数据库', count: 756, avgTime: 52 },
  { term: 'authentication', category: '安全', count: 634, avgTime: 38 },
  { term: 'api', category: '接口', count: 567, avgTime: 41 }
])

const autoOptimization = reactive({
  enabled: true,
  frequency: 'weekly',
  threshold: 75
})

const storageStrategy = reactive({
  retentionPeriod: '90d',
  compression: 'gzip',
  sharding: 'time'
})

const searchPerformanceData = ref([])

const filteredIndices = computed(() => {
  if (!filterStatus.value) return indices.value
  return indices.value.filter(index => index.status === filterStatus.value)
})

const getHealthText = (health) => {
  const texts = {
    green: '健康',
    yellow: '警告',
    red: '异常'
  }
  return texts[health] || '未知'
}

const getStatusClass = (status) => {
  const classes = {
    active: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    inactive: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
    optimizing: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
  return classes[status] || classes.inactive
}

const getStatusLabel = (status) => {
  const labels = {
    active: '活跃',
    inactive: '非活跃',
    optimizing: '优化中'
  }
  return labels[status] || '未知'
}

const optimizeIndices = async () => {
  isOptimizing.value = true
  
  try {
    // 模拟优化过程
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // 更新统计数据
    indexStats.searchSpeed = Math.max(50, indexStats.searchSpeed - Math.random() * 30)
    indexStats.efficiency = Math.min(99, indexStats.efficiency + Math.random() * 10)
    
    appStore.addNotification({
      type: 'success',
      message: '索引优化完成，搜索性能已提升'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '索引优化失败，请重试'
    })
  } finally {
    isOptimizing.value = false
  }
}

const optimizeIndex = (index) => {
  index.status = 'optimizing'
  appStore.addNotification({
    type: 'info',
    message: `开始优化索引: ${index.name}`
  })
  
  setTimeout(() => {
    index.status = 'active'
    appStore.addNotification({
      type: 'success',
      message: `索引 ${index.name} 优化完成`
    })
  }, 3000)
}

const reindexData = (index) => {
  appStore.addNotification({
    type: 'info',
    message: `开始重建索引: ${index.name}`
  })
}

const viewIndexDetails = (index) => {
  appStore.addNotification({
    type: 'info',
    message: `查看索引详情: ${index.name}`
  })
}

const deleteIndex = (index) => {
  if (confirm(`确定要删除索引 "${index.name}" 吗？此操作不可恢复！`)) {
    const indexIndex = indices.value.findIndex(i => i.name === index.name)
    if (indexIndex > -1) {
      indices.value.splice(indexIndex, 1)
      appStore.addNotification({
        type: 'success',
        message: '索引已删除'
      })
    }
  }
}

const toggleAutoOptimization = () => {
  autoOptimization.enabled = !autoOptimization.enabled
  appStore.addNotification({
    type: 'success',
    message: `自动优化已${autoOptimization.enabled ? '启用' : '禁用'}`
  })
}

const saveIndexConfig = () => {
  appStore.addNotification({
    type: 'success',
    message: '索引配置已保存'
  })
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}

// 初始化搜索性能数据
searchPerformanceData.value = Array.from({ length: 24 }, (_, i) => ({
  time: new Date(Date.now() - (23 - i) * 3600000).toISOString(),
  value: 100 + Math.random() * 100
}))
</script>
