<template>
  <div 
    v-if="show && logLine"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="$emit('close')"
  >
    <div 
      class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
        <div class="flex items-center space-x-4">
          <div class="p-3 bg-primary-100 dark:bg-primary-900/20 rounded-xl">
            <component :is="FileText" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">日志详情</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ formatTimestamp(logLine.timestamp) }} · {{ logLine.source || 'unknown' }}
            </p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <!-- 操作按钮 -->
          <button
            @click="copyLogLine"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="复制日志"
          >
            <component :is="Copy" class="w-5 h-5" />
          </button>
          <button
            @click="shareLogLine"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="分享日志"
          >
            <component :is="Share2" class="w-5 h-5" />
          </button>
          <button
            @click="addBookmark"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            title="添加书签"
          >
            <component :is="Bookmark" class="w-5 h-5" />
          </button>
          <button
            @click="$emit('close')"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <component :is="X" class="w-5 h-5" />
          </button>
        </div>
      </div>
      
      <!-- 内容 -->
      <div class="flex h-[calc(90vh-120px)]">
        <!-- 左侧：基本信息 -->
        <div class="w-1/3 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
          <div class="space-y-6">
            <!-- 基本信息卡片 -->
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
              <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <component :is="Info" class="w-4 h-4 mr-2 text-blue-500" />
                基本信息
              </h3>
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">时间戳</span>
                  <span class="text-sm font-mono text-gray-900 dark:text-white">
                    {{ formatFullTimestamp(logLine.timestamp) }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">日志级别</span>
                  <span 
                    class="px-2 py-1 text-xs font-bold rounded-md"
                    :class="getLevelBadgeClass(logLine.level)"
                  >
                    {{ logLine.level || 'INFO' }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">来源</span>
                  <span class="text-sm font-mono text-gray-900 dark:text-white">
                    {{ logLine.source || 'unknown' }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">格式</span>
                  <span class="text-sm font-mono text-gray-900 dark:text-white">
                    {{ logLine.format || 'generic' }}
                  </span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">大小</span>
                  <span class="text-sm text-gray-900 dark:text-white">
                    {{ formatBytes(logLine.raw?.length || 0) }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 元数据卡片 -->
            <div v-if="logLine.metadata && Object.keys(logLine.metadata).length > 0" class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
              <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <component :is="Tag" class="w-4 h-4 mr-2 text-green-500" />
                元数据
              </h3>
              <div class="space-y-3">
                <div v-if="logLine.metadata.ips" class="flex items-start justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">IP地址</span>
                  <div class="text-right">
                    <div 
                      v-for="ip in logLine.metadata.ips" 
                      :key="ip"
                      class="text-sm font-mono text-gray-900 dark:text-white"
                    >
                      {{ ip }}
                    </div>
                  </div>
                </div>
                <div v-if="logLine.metadata.urls" class="flex items-start justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">URL</span>
                  <div class="text-right max-w-48">
                    <div 
                      v-for="url in logLine.metadata.urls" 
                      :key="url"
                      class="text-sm font-mono text-gray-900 dark:text-white truncate"
                      :title="url"
                    >
                      {{ url }}
                    </div>
                  </div>
                </div>
                <div v-if="logLine.metadata.httpStatus" class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">HTTP状态</span>
                  <span 
                    class="px-2 py-1 text-xs font-medium rounded"
                    :class="getHttpStatusClass(logLine.metadata.httpStatus)"
                  >
                    {{ logLine.metadata.httpStatus }}
                  </span>
                </div>
                <div v-if="logLine.metadata.responseTime" class="flex items-center justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">响应时间</span>
                  <span class="text-sm text-gray-900 dark:text-white">
                    {{ logLine.metadata.responseTime }}ms
                  </span>
                </div>
                <div v-if="logLine.metadata.emails" class="flex items-start justify-between">
                  <span class="text-sm text-gray-600 dark:text-gray-400">邮箱</span>
                  <div class="text-right">
                    <div 
                      v-for="email in logLine.metadata.emails" 
                      :key="email"
                      class="text-sm font-mono text-gray-900 dark:text-white"
                    >
                      {{ email }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 字段信息 -->
            <div v-if="logLine.fields && Object.keys(logLine.fields).length > 0" class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4">
              <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                <component :is="Database" class="w-4 h-4 mr-2 text-purple-500" />
                解析字段
              </h3>
              <div class="space-y-2">
                <div 
                  v-for="(value, key) in logLine.fields" 
                  :key="key"
                  class="flex items-start justify-between py-1"
                >
                  <span class="text-sm text-gray-600 dark:text-gray-400 capitalize">{{ key }}</span>
                  <span class="text-sm font-mono text-gray-900 dark:text-white max-w-32 truncate" :title="value">
                    {{ value || '-' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 右侧：日志内容 -->
        <div class="flex-1 flex flex-col">
          <!-- 内容标签页 -->
          <div class="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
            <button
              @click="activeTab = 'formatted'"
              class="px-4 py-3 text-sm font-medium border-b-2 transition-colors"
              :class="activeTab === 'formatted'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-white dark:bg-gray-800'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'"
            >
              格式化内容
            </button>
            <button
              @click="activeTab = 'raw'"
              class="px-4 py-3 text-sm font-medium border-b-2 transition-colors"
              :class="activeTab === 'raw'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-white dark:bg-gray-800'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'"
            >
              原始内容
            </button>
            <button
              @click="activeTab = 'json'"
              class="px-4 py-3 text-sm font-medium border-b-2 transition-colors"
              :class="activeTab === 'json'
                ? 'border-primary-500 text-primary-600 dark:text-primary-400 bg-white dark:bg-gray-800'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300'"
            >
              JSON视图
            </button>
          </div>
          
          <!-- 内容区域 -->
          <div class="flex-1 p-6 overflow-y-auto">
            <!-- 格式化内容 -->
            <div v-if="activeTab === 'formatted'" class="space-y-4">
              <div class="bg-gray-900 text-gray-300 p-4 rounded-lg font-mono text-sm leading-relaxed">
                <div class="flex items-start space-x-4">
                  <div class="text-gray-500 text-xs w-20 flex-shrink-0">
                    {{ formatTimestamp(logLine.timestamp) }}
                  </div>
                  <div 
                    class="px-2 py-1 text-xs font-bold rounded"
                    :class="getLevelBadgeClass(logLine.level)"
                  >
                    {{ logLine.level || 'INFO' }}
                  </div>
                  <div class="text-gray-400 text-xs">
                    [{{ logLine.source || 'unknown' }}]
                  </div>
                  <div class="flex-1 text-gray-200">
                    {{ logLine.message || logLine.raw }}
                  </div>
                </div>
              </div>
              
              <!-- 高亮关键信息 -->
              <div v-if="highlightedContent" class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 class="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">检测到的关键信息</h4>
                <div class="space-y-2 text-sm">
                  <div v-for="item in highlightedContent" :key="item.type" class="flex items-center space-x-2">
                    <span class="px-2 py-1 bg-yellow-200 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded text-xs font-medium">
                      {{ item.type }}
                    </span>
                    <span class="font-mono text-gray-900 dark:text-white">{{ item.value }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 原始内容 -->
            <div v-if="activeTab === 'raw'" class="bg-gray-900 text-gray-300 p-4 rounded-lg font-mono text-sm leading-relaxed whitespace-pre-wrap">
              {{ logLine.raw }}
            </div>
            
            <!-- JSON视图 -->
            <div v-if="activeTab === 'json'" class="bg-gray-900 text-gray-300 p-4 rounded-lg font-mono text-sm leading-relaxed">
              <pre class="whitespace-pre-wrap">{{ formatJsonContent }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { format } from 'date-fns'
import { 
  FileText, 
  Copy, 
  Share2, 
  Bookmark, 
  X, 
  Info, 
  Tag, 
  Database 
} from 'lucide-vue-next'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  logLine: {
    type: Object,
    default: null
  }
})

defineEmits(['close'])

const activeTab = ref('formatted')

const formatTimestamp = (timestamp) => {
  return format(timestamp, 'HH:mm:ss.SSS')
}

const formatFullTimestamp = (timestamp) => {
  return format(timestamp, 'yyyy-MM-dd HH:mm:ss.SSS')
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getLevelBadgeClass = (level) => {
  const classes = {
    ERROR: 'bg-red-900/50 text-red-300 border border-red-700',
    WARN: 'bg-yellow-900/50 text-yellow-300 border border-yellow-700',
    INFO: 'bg-blue-900/50 text-blue-300 border border-blue-700',
    DEBUG: 'bg-gray-900/50 text-gray-300 border border-gray-700'
  }
  return classes[level] || classes.INFO
}

const getHttpStatusClass = (status) => {
  if (status >= 500) return 'bg-red-900/30 text-red-300 border border-red-700'
  if (status >= 400) return 'bg-yellow-900/30 text-yellow-300 border border-yellow-700'
  if (status >= 200 && status < 300) return 'bg-green-900/30 text-green-300 border border-green-700'
  return 'bg-gray-900/30 text-gray-300 border border-gray-700'
}

const highlightedContent = computed(() => {
  if (!props.logLine?.metadata) return null
  
  const items = []
  const metadata = props.logLine.metadata
  
  if (metadata.ips) {
    metadata.ips.forEach(ip => items.push({ type: 'IP地址', value: ip }))
  }
  if (metadata.urls) {
    metadata.urls.forEach(url => items.push({ type: 'URL', value: url }))
  }
  if (metadata.emails) {
    metadata.emails.forEach(email => items.push({ type: '邮箱', value: email }))
  }
  if (metadata.httpStatus) {
    items.push({ type: 'HTTP状态', value: metadata.httpStatus })
  }
  if (metadata.responseTime) {
    items.push({ type: '响应时间', value: `${metadata.responseTime}ms` })
  }
  
  return items.length > 0 ? items : null
})

const formatJsonContent = computed(() => {
  if (!props.logLine) return ''
  
  const jsonData = {
    id: props.logLine.id,
    timestamp: props.logLine.timestamp,
    level: props.logLine.level,
    source: props.logLine.source,
    message: props.logLine.message,
    format: props.logLine.format,
    fields: props.logLine.fields,
    metadata: props.logLine.metadata,
    raw: props.logLine.raw
  }
  
  return JSON.stringify(jsonData, null, 2)
})

const copyLogLine = () => {
  const text = props.logLine.raw || props.logLine.message
  navigator.clipboard.writeText(text)
}

const shareLogLine = () => {
  const text = `${formatFullTimestamp(props.logLine.timestamp)} [${props.logLine.level}] ${props.logLine.source}: ${props.logLine.message}`
  navigator.clipboard.writeText(text)
}

const addBookmark = () => {
  // 添加书签逻辑
}
</script>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
