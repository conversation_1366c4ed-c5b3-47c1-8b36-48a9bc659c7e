<template>
  <div class="h-full overflow-auto">
    <div v-if="logStore.paginatedLogs.length > 0" class="divide-y divide-gray-200 dark:divide-gray-700">
      <div
        v-for="(log, index) in logStore.paginatedLogs"
        :key="log.id"
        class="group relative p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
        @click="selectLog(log)"
      >
        <div class="flex items-start space-x-4">
          <!-- 日志级别指示器 -->
          <div class="flex-shrink-0 mt-1">
            <div
              class="w-3 h-3 rounded-full"
              :class="getLevelColor(log.level)"
            ></div>
          </div>

          <!-- 日志内容 -->
          <div class="flex-1 min-w-0">
            <!-- 头部信息 -->
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-3">
                <!-- 级别标签 -->
                <span
                  class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium uppercase"
                  :class="getLevelBadgeClass(log.level)"
                >
                  {{ log.level }}
                </span>

                <!-- 来源标签 -->
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ log.source }}
                </span>

                <!-- 服务标签 -->
                <span class="text-sm text-gray-500 dark:text-gray-400">
                  {{ log.service }}
                </span>
              </div>

              <!-- 时间戳 -->
              <span class="text-sm text-gray-500 dark:text-gray-400 font-mono">
                {{ formatTimestamp(log.timestamp) }}
              </span>
            </div>

            <!-- 日志消息 -->
            <div class="log-entry text-gray-900 dark:text-gray-100 mb-2">
              <span v-html="highlightSearchTerm(log.message)"></span>
            </div>

            <!-- 元数据 -->
            <div v-if="log.metadata" class="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
              <span v-if="log.metadata.userId">User: {{ log.metadata.userId }}</span>
              <span v-if="log.metadata.requestId">Request: {{ log.metadata.requestId }}</span>
              <span v-if="log.metadata.ip">IP: {{ log.metadata.ip }}</span>
              <span v-if="log.metadata.responseTime">{{ log.metadata.responseTime }}</span>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex-shrink-0">
            <button
              @click.stop="copyLog(log)"
              class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded opacity-0 group-hover:opacity-100 transition-opacity"
              title="复制日志条目"
            >
              <component :is="Copy" class="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div
      v-else
      class="flex items-center justify-center h-full"
    >
      <div class="text-center">
        <component :is="FileText" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">未找到日志</h3>
        <p class="text-gray-500 dark:text-gray-400">
          {{ logStore.logs.length === 0
            ? '尚未加载任何日志。'
            : '请尝试调整搜索条件或过滤器。'
          }}
        </p>
      </div>
    </div>
  </div>

  <!-- 日志详情模态框 -->
  <LogDetailModal
    v-if="selectedLog"
    :log="selectedLog"
    @close="selectedLog = null"
  />
</template>

<script setup>
import { ref } from 'vue'
import { useLogStore } from '@/stores/logStore'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import {
  Copy,
  FileText
} from 'lucide-vue-next'
import LogDetailModal from './LogDetailModal.vue'
import { highlightLogMessage, getLogLevelStyle } from '@/utils/logHighlight'

const logStore = useLogStore()
const appStore = useAppStore()

const selectedLog = ref(null)

// 样式相关方法
const getLevelColor = (level) => {
  return getLogLevelStyle(level).color
}

const getLevelBadgeClass = (level) => {
  return getLogLevelStyle(level).badge
}

// 工具方法
const formatTimestamp = (timestamp) => {
  return format(new Date(timestamp), 'MM-dd HH:mm:ss.SSS')
}

const highlightSearchTerm = (message) => {
  return highlightLogMessage(message, logStore.searchQuery)
}

// 操作方法
const selectLog = (log) => {
  selectedLog.value = log
}

const copyLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '📋 日志条目已复制到剪贴板'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 复制日志条目失败'
    })
  }
}

const shareLog = async (log) => {
  const logText = `[${log.timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}: ${log.message}`

  try {
    await navigator.clipboard.writeText(logText)
    appStore.addNotification({
      type: 'success',
      message: '🔗 日志链接已复制，可以分享给团队成员'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '❌ 分享失败'
    })
  }
}

const bookmarkLog = (log) => {
  // 这里可以实现书签功能
  appStore.addNotification({
    type: 'success',
    message: '⭐ 日志已添加到书签'
  })
}

const clearFilters = () => {
  logStore.setSearchQuery('')
  logStore.setLogLevel('all')
  logStore.setSelectedSources([])
}

const refreshData = () => {
  appStore.addNotification({
    type: 'info',
    message: '🔄 正在刷新日志数据...'
  })
}
</script>

<style scoped>
mark {
  padding: 0 2px;
  border-radius: 2px;
}
</style>
