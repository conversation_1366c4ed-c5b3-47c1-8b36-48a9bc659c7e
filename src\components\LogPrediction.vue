<template>
  <div class="space-y-6">
    <!-- 预测概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志预测与容量规划</h3>
        <div class="flex items-center space-x-4">
          <select v-model="predictionPeriod" class="input w-32">
            <option value="1d">未来1天</option>
            <option value="7d">未来7天</option>
            <option value="30d">未来30天</option>
            <option value="90d">未来90天</option>
          </select>
          <button
            @click="runPrediction"
            class="btn-primary"
            :disabled="isPredicting"
          >
            <component :is="isPredicting ? Loader : TrendingUp" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isPredicting }" />
            {{ isPredicting ? '预测中...' : '运行预测' }}
          </button>
        </div>
      </div>
      
      <!-- 预测指标 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ predictionMetrics.volumeGrowth }}%</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">日志量增长</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ predictionMetrics.storageNeeded }}GB</div>
          <div class="text-sm text-green-600 dark:text-green-400">预计存储需求</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ predictionMetrics.errorIncrease }}%</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">错误率变化</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ predictionMetrics.accuracy }}%</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">预测准确度</div>
        </div>
      </div>
    </div>
    
    <!-- 预测图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 日志量预测 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">日志量预测</h4>
        <div class="h-64">
          <VolumePredictionChart :data="volumePredictionData" />
        </div>
      </div>
      
      <!-- 错误率预测 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">错误率预测</h4>
        <div class="h-64">
          <ErrorRatePredictionChart :data="errorRatePredictionData" />
        </div>
      </div>
    </div>
    
    <!-- 容量规划 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">容量规划建议</h4>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 存储容量 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">存储容量</h5>
          <div class="space-y-3">
            <div class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">当前使用</span>
                <span class="text-sm text-gray-600 dark:text-gray-400">{{ capacityPlanning.storage.current }}GB</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-blue-500 h-2 rounded-full"
                  :style="{ width: `${capacityPlanning.storage.currentPercent}%` }"
                ></div>
              </div>
            </div>
            
            <div class="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">预计需求</span>
                <span class="text-sm text-yellow-600 dark:text-yellow-400">{{ capacityPlanning.storage.predicted }}GB</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-yellow-500 h-2 rounded-full"
                  :style="{ width: `${capacityPlanning.storage.predictedPercent}%` }"
                ></div>
              </div>
            </div>
            
            <div class="text-xs text-gray-500">
              建议扩容至 {{ capacityPlanning.storage.recommended }}GB
            </div>
          </div>
        </div>
        
        <!-- 处理能力 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">处理能力</h5>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">当前吞吐量</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ capacityPlanning.processing.current }}/s</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">峰值需求</span>
              <span class="text-sm font-medium text-yellow-600">{{ capacityPlanning.processing.peak }}/s</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">建议配置</span>
              <span class="text-sm font-medium text-green-600">{{ capacityPlanning.processing.recommended }}/s</span>
            </div>
            <div class="text-xs text-gray-500 mt-2">
              建议增加 {{ capacityPlanning.processing.additionalNodes }} 个处理节点
            </div>
          </div>
        </div>
        
        <!-- 成本预估 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">成本预估</h5>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">当前月成本</span>
              <span class="text-sm font-medium text-gray-900 dark:text-white">¥{{ capacityPlanning.cost.current }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">预计月成本</span>
              <span class="text-sm font-medium text-yellow-600">¥{{ capacityPlanning.cost.predicted }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">优化后成本</span>
              <span class="text-sm font-medium text-green-600">¥{{ capacityPlanning.cost.optimized }}</span>
            </div>
            <div class="text-xs text-gray-500 mt-2">
              通过优化可节省 ¥{{ capacityPlanning.cost.savings }}/月
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 预测模型配置 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">预测模型配置</h4>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 模型参数 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">模型参数</h5>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                历史数据窗口
              </label>
              <select v-model="modelConfig.historyWindow" class="input">
                <option value="7d">7天</option>
                <option value="30d">30天</option>
                <option value="90d">90天</option>
                <option value="1y">1年</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                季节性调整
              </label>
              <div class="flex items-center space-x-2">
                <input
                  type="checkbox"
                  v-model="modelConfig.seasonalAdjustment"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="text-sm text-gray-600 dark:text-gray-400">启用季节性模式识别</span>
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                异常值处理
              </label>
              <select v-model="modelConfig.outlierHandling" class="input">
                <option value="remove">移除异常值</option>
                <option value="adjust">调整异常值</option>
                <option value="keep">保留异常值</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                置信区间: {{ modelConfig.confidenceInterval }}%
              </label>
              <input
                type="range"
                v-model="modelConfig.confidenceInterval"
                min="80"
                max="99"
                class="w-full"
              />
            </div>
          </div>
        </div>
        
        <!-- 模型性能 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">模型性能</h5>
          <div class="space-y-4">
            <div class="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">预测准确度</span>
                <span class="text-sm text-green-600">{{ modelPerformance.accuracy }}%</span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="bg-green-500 h-2 rounded-full"
                  :style="{ width: `${modelPerformance.accuracy}%` }"
                ></div>
              </div>
            </div>
            
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span class="text-gray-600 dark:text-gray-400">MAE:</span>
                <span class="font-medium text-gray-900 dark:text-white ml-2">{{ modelPerformance.mae }}</span>
              </div>
              <div>
                <span class="text-gray-600 dark:text-gray-400">RMSE:</span>
                <span class="font-medium text-gray-900 dark:text-white ml-2">{{ modelPerformance.rmse }}</span>
              </div>
              <div>
                <span class="text-gray-600 dark:text-gray-400">MAPE:</span>
                <span class="font-medium text-gray-900 dark:text-white ml-2">{{ modelPerformance.mape }}%</span>
              </div>
              <div>
                <span class="text-gray-600 dark:text-gray-400">R²:</span>
                <span class="font-medium text-gray-900 dark:text-white ml-2">{{ modelPerformance.r2 }}</span>
              </div>
            </div>
            
            <div class="text-xs text-gray-500">
              最后训练时间: {{ modelPerformance.lastTrained }}
            </div>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end mt-6">
        <button
          @click="retrainModel"
          class="btn-secondary mr-3"
          :disabled="isRetraining"
        >
          <component :is="isRetraining ? Loader : RefreshCw" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isRetraining }" />
          {{ isRetraining ? '重训练中...' : '重新训练模型' }}
        </button>
        <button
          @click="saveModelConfig"
          class="btn-primary"
        >
          <component :is="Save" class="w-4 h-4 mr-2" />
          保存配置
        </button>
      </div>
    </div>
    
    <!-- 预警设置 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">容量预警设置</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">存储预警</h5>
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                存储使用率阈值: {{ alertConfig.storage.threshold }}%
              </label>
              <input
                type="range"
                v-model="alertConfig.storage.threshold"
                min="70"
                max="95"
                class="w-full"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                提前预警天数
              </label>
              <select v-model="alertConfig.storage.advanceDays" class="input">
                <option value="3">3天</option>
                <option value="7">7天</option>
                <option value="14">14天</option>
                <option value="30">30天</option>
              </select>
            </div>
          </div>
        </div>
        
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">性能预警</h5>
          <div class="space-y-3">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                处理延迟阈值: {{ alertConfig.performance.latencyThreshold }}ms
              </label>
              <input
                type="range"
                v-model="alertConfig.performance.latencyThreshold"
                min="100"
                max="2000"
                step="100"
                class="w-full"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                错误率阈值: {{ alertConfig.performance.errorRateThreshold }}%
              </label>
              <input
                type="range"
                v-model="alertConfig.performance.errorRateThreshold"
                min="1"
                max="10"
                step="0.5"
                class="w-full"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { 
  TrendingUp, 
  Loader, 
  RefreshCw, 
  Save 
} from 'lucide-vue-next'

// 导入图表组件
import VolumePredictionChart from '@/components/charts/VolumePredictionChart.vue'
import ErrorRatePredictionChart from '@/components/charts/ErrorRatePredictionChart.vue'

const appStore = useAppStore()

const isPredicting = ref(false)
const isRetraining = ref(false)
const predictionPeriod = ref('7d')

const predictionMetrics = reactive({
  volumeGrowth: 15.3,
  storageNeeded: 2.8,
  errorIncrease: -2.1,
  accuracy: 94.2
})

const capacityPlanning = reactive({
  storage: {
    current: 1.2,
    currentPercent: 60,
    predicted: 2.8,
    predictedPercent: 85,
    recommended: 4.0
  },
  processing: {
    current: 1200,
    peak: 2800,
    recommended: 3500,
    additionalNodes: 2
  },
  cost: {
    current: 8500,
    predicted: 12800,
    optimized: 11200,
    savings: 1600
  }
})

const modelConfig = reactive({
  historyWindow: '30d',
  seasonalAdjustment: true,
  outlierHandling: 'adjust',
  confidenceInterval: 95
})

const modelPerformance = reactive({
  accuracy: 94.2,
  mae: 0.12,
  rmse: 0.18,
  mape: 3.8,
  r2: 0.92,
  lastTrained: '2024-01-15 10:30:00'
})

const alertConfig = reactive({
  storage: {
    threshold: 80,
    advanceDays: 7
  },
  performance: {
    latencyThreshold: 500,
    errorRateThreshold: 5.0
  }
})

const volumePredictionData = ref([])
const errorRatePredictionData = ref([])

const runPrediction = async () => {
  isPredicting.value = true
  
  try {
    // 模拟预测过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新预测指标
    predictionMetrics.volumeGrowth = Math.random() * 30 + 5
    predictionMetrics.storageNeeded = Math.random() * 5 + 1
    predictionMetrics.errorIncrease = (Math.random() - 0.5) * 10
    predictionMetrics.accuracy = Math.random() * 10 + 90
    
    // 生成预测数据
    generatePredictionData()
    
    appStore.addNotification({
      type: 'success',
      message: '预测分析完成，容量规划已更新'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '预测分析失败，请重试'
    })
  } finally {
    isPredicting.value = false
  }
}

const retrainModel = async () => {
  isRetraining.value = true
  
  try {
    // 模拟重训练过程
    await new Promise(resolve => setTimeout(resolve, 8000))
    
    // 更新模型性能
    modelPerformance.accuracy = Math.min(99, modelPerformance.accuracy + Math.random() * 2)
    modelPerformance.mae = Math.max(0.05, modelPerformance.mae - Math.random() * 0.05)
    modelPerformance.rmse = Math.max(0.08, modelPerformance.rmse - Math.random() * 0.05)
    modelPerformance.mape = Math.max(1, modelPerformance.mape - Math.random() * 1)
    modelPerformance.r2 = Math.min(0.99, modelPerformance.r2 + Math.random() * 0.05)
    modelPerformance.lastTrained = new Date().toLocaleString('zh-CN')
    
    appStore.addNotification({
      type: 'success',
      message: '模型重训练完成，性能指标已提升'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '模型重训练失败，请重试'
    })
  } finally {
    isRetraining.value = false
  }
}

const saveModelConfig = () => {
  appStore.addNotification({
    type: 'success',
    message: '模型配置已保存'
  })
}

const generatePredictionData = () => {
  const now = new Date()
  const days = predictionPeriod.value === '1d' ? 1 : 
               predictionPeriod.value === '7d' ? 7 : 
               predictionPeriod.value === '30d' ? 30 : 90
  
  // 生成历史数据（过去30天）
  const historicalData = Array.from({ length: 30 }, (_, i) => {
    const date = new Date(now.getTime() - (29 - i) * 24 * 3600000)
    return {
      time: date.toISOString(),
      actual: 1000 + Math.sin(i / 7) * 200 + Math.random() * 100,
      type: 'historical'
    }
  })
  
  // 生成预测数据
  const predictionData = Array.from({ length: days }, (_, i) => {
    const date = new Date(now.getTime() + (i + 1) * 24 * 3600000)
    const trend = 1000 + (i * 10) // 增长趋势
    const seasonal = Math.sin((i + 30) / 7) * 200 // 季节性
    const noise = Math.random() * 50 // 噪声
    
    return {
      time: date.toISOString(),
      predicted: trend + seasonal + noise,
      upper: trend + seasonal + noise + 100,
      lower: trend + seasonal + noise - 100,
      type: 'prediction'
    }
  })
  
  volumePredictionData.value = [...historicalData, ...predictionData]
  
  // 生成错误率预测数据
  const errorHistorical = Array.from({ length: 30 }, (_, i) => {
    const date = new Date(now.getTime() - (29 - i) * 24 * 3600000)
    return {
      time: date.toISOString(),
      actual: 2 + Math.sin(i / 5) * 1 + Math.random() * 0.5,
      type: 'historical'
    }
  })
  
  const errorPrediction = Array.from({ length: days }, (_, i) => {
    const date = new Date(now.getTime() + (i + 1) * 24 * 3600000)
    const base = 2 + Math.sin((i + 30) / 5) * 1
    
    return {
      time: date.toISOString(),
      predicted: base + Math.random() * 0.3,
      upper: base + 0.5,
      lower: Math.max(0, base - 0.5),
      type: 'prediction'
    }
  })
  
  errorRatePredictionData.value = [...errorHistorical, ...errorPrediction]
}

// 初始化数据
generatePredictionData()
</script>
