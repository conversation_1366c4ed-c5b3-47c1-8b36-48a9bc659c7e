<template>
  <div class="space-y-6">
    <!-- 流处理控制面板 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志流处理器</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="isProcessing ? 'bg-green-500 animate-pulse' : 'bg-gray-400'"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ isProcessing ? '处理中' : '已停止' }}
            </span>
          </div>
          <button
            @click="toggleProcessing"
            class="btn-primary"
          >
            <component :is="isProcessing ? Pause : Play" class="w-4 h-4 mr-2" />
            {{ isProcessing ? '暂停' : '开始' }}
          </button>
        </div>
      </div>
      
      <!-- 处理统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ stats.processed }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">已处理</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ stats.throughput }}/s</div>
          <div class="text-sm text-green-600 dark:text-green-400">处理速度</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ stats.errors }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">处理错误</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ stats.enriched }}</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">已增强</div>
        </div>
      </div>
    </div>
    
    <!-- 流处理规则 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">处理规则</h4>
        <button
          @click="showAddRule = true"
          class="btn-primary"
        >
          <component :is="Plus" class="w-4 h-4 mr-2" />
          添加规则
        </button>
      </div>
      
      <div class="space-y-4">
        <div
          v-for="rule in processingRules"
          :key="rule.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <h5 class="font-medium text-gray-900 dark:text-white">{{ rule.name }}</h5>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="rule.enabled 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'"
              >
                {{ rule.enabled ? '启用' : '禁用' }}
              </span>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getRuleTypeClass(rule.type)"
              >
                {{ getRuleTypeLabel(rule.type) }}
              </span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="toggleRule(rule)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="rule.enabled ? Pause : Play" class="w-4 h-4" />
              </button>
              <button
                @click="editRule(rule)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="Edit" class="w-4 h-4" />
              </button>
              <button
                @click="deleteRule(rule)"
                class="p-2 text-red-400 hover:text-red-600 rounded"
              >
                <component :is="Trash2" class="w-4 h-4" />
              </button>
            </div>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ rule.description }}</p>
          <div class="text-xs text-gray-500">
            <span>条件: {{ rule.condition }}</span>
            <span class="ml-4">动作: {{ rule.action }}</span>
            <span class="ml-4">处理: {{ rule.processedCount }} 条</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 实时流监控 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">实时流监控</h4>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 流量图表 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">处理流量</h5>
          <div class="h-48">
            <StreamThroughputChart :data="throughputData" />
          </div>
        </div>
        
        <!-- 延迟监控 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">处理延迟</h5>
          <div class="h-48">
            <LatencyChart :data="latencyData" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 数据增强配置 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">数据增强</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- IP地理位置 -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-gray-900 dark:text-white">IP地理位置解析</h5>
            <button
              @click="toggleEnrichment('geoip')"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="enrichments.geoip ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="enrichments.geoip ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400">自动解析IP地址的地理位置信息</p>
        </div>
        
        <!-- 用户代理解析 -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-gray-900 dark:text-white">用户代理解析</h5>
            <button
              @click="toggleEnrichment('useragent')"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="enrichments.useragent ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="enrichments.useragent ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400">解析浏览器和设备信息</p>
        </div>
        
        <!-- 威胁情报 -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-gray-900 dark:text-white">威胁情报</h5>
            <button
              @click="toggleEnrichment('threat')"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="enrichments.threat ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="enrichments.threat ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400">检查恶意IP和域名</p>
        </div>
        
        <!-- 关联分析 -->
        <div class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-gray-900 dark:text-white">关联分析</h5>
            <button
              @click="toggleEnrichment('correlation')"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="enrichments.correlation ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="enrichments.correlation ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400">自动关联相关日志事件</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { 
  Play, 
  Pause, 
  Plus, 
  Edit, 
  Trash2 
} from 'lucide-vue-next'

// 导入图表组件
import StreamThroughputChart from '@/components/charts/StreamThroughputChart.vue'
import LatencyChart from '@/components/charts/LatencyChart.vue'

const appStore = useAppStore()

const isProcessing = ref(false)
const showAddRule = ref(false)

const stats = reactive({
  processed: 12547,
  throughput: 245,
  errors: 12,
  enriched: 8934
})

const enrichments = reactive({
  geoip: true,
  useragent: true,
  threat: false,
  correlation: true
})

const processingRules = ref([
  {
    id: 1,
    name: '错误日志增强',
    description: '为错误级别日志添加上下文信息和相关事件',
    type: 'enrichment',
    condition: 'level == "error"',
    action: 'add_context',
    enabled: true,
    processedCount: 234
  },
  {
    id: 2,
    name: '安全事件检测',
    description: '检测可疑的登录尝试和安全威胁',
    type: 'security',
    condition: 'message contains "failed login"',
    action: 'security_alert',
    enabled: true,
    processedCount: 45
  },
  {
    id: 3,
    name: '性能异常标记',
    description: '标记响应时间异常的请求',
    type: 'performance',
    condition: 'response_time > 1000',
    action: 'mark_slow',
    enabled: false,
    processedCount: 89
  }
])

const throughputData = ref([])
const latencyData = ref([])

let processingInterval = null

const toggleProcessing = () => {
  isProcessing.value = !isProcessing.value
  
  if (isProcessing.value) {
    startProcessing()
  } else {
    stopProcessing()
  }
  
  appStore.addNotification({
    type: 'success',
    message: `流处理器已${isProcessing.value ? '启动' : '停止'}`
  })
}

const startProcessing = () => {
  processingInterval = setInterval(() => {
    // 模拟处理统计更新
    stats.processed += Math.floor(Math.random() * 10) + 5
    stats.throughput = Math.floor(Math.random() * 100) + 200
    stats.errors += Math.floor(Math.random() * 3)
    stats.enriched += Math.floor(Math.random() * 8) + 3
    
    // 更新图表数据
    updateChartData()
  }, 2000)
}

const stopProcessing = () => {
  if (processingInterval) {
    clearInterval(processingInterval)
    processingInterval = null
  }
}

const updateChartData = () => {
  const now = new Date()
  
  // 更新吞吐量数据
  throughputData.value.push({
    time: now.toISOString(),
    value: stats.throughput
  })
  
  // 保持最近50个数据点
  if (throughputData.value.length > 50) {
    throughputData.value.shift()
  }
  
  // 更新延迟数据
  latencyData.value.push({
    time: now.toISOString(),
    value: Math.floor(Math.random() * 100) + 20
  })
  
  if (latencyData.value.length > 50) {
    latencyData.value.shift()
  }
}

const toggleRule = (rule) => {
  rule.enabled = !rule.enabled
  appStore.addNotification({
    type: 'success',
    message: `规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`
  })
}

const editRule = (rule) => {
  // 编辑规则逻辑
  appStore.addNotification({
    type: 'info',
    message: `编辑规则: ${rule.name}`
  })
}

const deleteRule = (rule) => {
  if (confirm(`确定要删除规则 "${rule.name}" 吗？`)) {
    const index = processingRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      processingRules.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '规则已删除'
      })
    }
  }
}

const toggleEnrichment = (type) => {
  enrichments[type] = !enrichments[type]
  appStore.addNotification({
    type: 'success',
    message: `数据增强 "${type}" 已${enrichments[type] ? '启用' : '禁用'}`
  })
}

const getRuleTypeClass = (type) => {
  const classes = {
    enrichment: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    security: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    performance: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[type] || classes.enrichment
}

const getRuleTypeLabel = (type) => {
  const labels = {
    enrichment: '增强',
    security: '安全',
    performance: '性能'
  }
  return labels[type] || type
}

onMounted(() => {
  // 初始化图表数据
  for (let i = 0; i < 20; i++) {
    updateChartData()
  }
})

onUnmounted(() => {
  stopProcessing()
})
</script>
