<template>
  <div class="log-table h-full flex flex-col bg-white dark:bg-gray-900">
    <!-- 表格头部控制 -->
    <div class="table-header bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <component :is="Table" class="w-4 h-4 text-gray-500" />
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">表格视图</span>
          </div>
          
          <div class="flex items-center space-x-2">
            <button
              @click="toggleColumnConfig"
              class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              title="列配置"
            >
              <component :is="Settings" class="w-4 h-4" />
            </button>
            
            <button
              @click="refreshData"
              class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              title="刷新"
            >
              <component :is="RefreshCw" class="w-4 h-4" />
            </button>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-500">
            显示 {{ visibleLogs.length }} / {{ totalLogs }} 条
          </div>
          
          <select v-model="pageSize" class="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1">
            <option value="50">50 条/页</option>
            <option value="100">100 条/页</option>
            <option value="200">200 条/页</option>
            <option value="500">500 条/页</option>
          </select>
        </div>
      </div>
    </div>
    
    <!-- 表格内容 -->
    <div class="flex-1 overflow-hidden">
      <div class="h-full overflow-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- 表头 -->
          <thead class="bg-gray-50 dark:bg-gray-800 sticky top-0 z-10">
            <tr>
              <th 
                v-for="column in visibleColumns" 
                :key="column.key"
                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                :class="{ 'bg-gray-100 dark:bg-gray-700': sortColumn === column.key }"
                @click="handleSort(column.key)"
              >
                <div class="flex items-center space-x-1">
                  <span>{{ column.label }}</span>
                  <component 
                    v-if="sortColumn === column.key"
                    :is="sortDirection === 'asc' ? ChevronUp : ChevronDown" 
                    class="w-3 h-3" 
                  />
                </div>
              </th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          
          <!-- 表体 -->
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr
              v-for="(log, index) in paginatedLogs"
              :key="log.id"
              class="hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors cursor-pointer"
              :class="{ 'bg-blue-50 dark:bg-blue-900/20': selectedLogs.includes(log.id) }"
              @click="selectLog(log)"
              @dblclick="showLogDetails(log)"
            >
              <td 
                v-for="column in visibleColumns" 
                :key="column.key"
                class="px-4 py-3 whitespace-nowrap"
                :class="column.class || ''"
              >
                <!-- 时间戳 -->
                <div v-if="column.key === 'timestamp'" class="text-sm text-gray-900 dark:text-white">
                  {{ formatTimestamp(log.timestamp) }}
                </div>
                
                <!-- 日志级别 -->
                <div v-else-if="column.key === 'level'">
                  <span 
                    class="px-2 py-1 text-xs font-medium rounded-full"
                    :class="getLevelClass(log.level)"
                  >
                    {{ log.level }}
                  </span>
                </div>
                
                <!-- 来源 -->
                <div v-else-if="column.key === 'source'" class="text-sm text-gray-900 dark:text-white">
                  <div class="flex items-center space-x-2">
                    <div 
                      class="w-2 h-2 rounded-full"
                      :class="getSourceStatusClass(log.source)"
                    ></div>
                    <span>{{ log.source }}</span>
                  </div>
                </div>
                
                <!-- 消息内容 -->
                <div v-else-if="column.key === 'message'" class="text-sm text-gray-900 dark:text-white">
                  <div class="max-w-md truncate" :title="log.message">
                    {{ log.message }}
                  </div>
                </div>
                
                <!-- 用户 -->
                <div v-else-if="column.key === 'user'" class="text-sm text-gray-900 dark:text-white">
                  {{ log.user || '-' }}
                </div>
                
                <!-- IP地址 -->
                <div v-else-if="column.key === 'ip'" class="text-sm text-gray-900 dark:text-white font-mono">
                  {{ log.ip || '-' }}
                </div>
                
                <!-- 响应时间 -->
                <div v-else-if="column.key === 'responseTime'" class="text-sm text-gray-900 dark:text-white">
                  <span v-if="log.responseTime" :class="getResponseTimeClass(log.responseTime)">
                    {{ log.responseTime }}ms
                  </span>
                  <span v-else>-</span>
                </div>
                
                <!-- 状态码 -->
                <div v-else-if="column.key === 'statusCode'" class="text-sm text-gray-900 dark:text-white">
                  <span v-if="log.statusCode" :class="getStatusCodeClass(log.statusCode)">
                    {{ log.statusCode }}
                  </span>
                  <span v-else>-</span>
                </div>
                
                <!-- 其他字段 -->
                <div v-else class="text-sm text-gray-900 dark:text-white">
                  {{ log[column.key] || '-' }}
                </div>
              </td>
              
              <!-- 操作列 -->
              <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click.stop="copyLog(log)"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    title="复制"
                  >
                    <component :is="Copy" class="w-4 h-4" />
                  </button>
                  <button
                    @click.stop="showContext(log)"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    title="查看上下文"
                  >
                    <component :is="Eye" class="w-4 h-4" />
                  </button>
                  <button
                    @click.stop="addBookmark(log)"
                    class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                    title="添加书签"
                  >
                    <component :is="Bookmark" class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    
    <!-- 分页控制 -->
    <div class="pagination bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-700 dark:text-gray-300">
            第 {{ (currentPage - 1) * parseInt(pageSize) + 1 }} - {{ Math.min(currentPage * parseInt(pageSize), totalLogs) }} 条，共 {{ totalLogs }} 条
          </span>
        </div>
        
        <div class="flex items-center space-x-2">
          <button
            @click="goToPage(1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            首页
          </button>
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          
          <div class="flex items-center space-x-1">
            <button
              v-for="page in visiblePages"
              :key="page"
              @click="goToPage(page)"
              class="px-3 py-1 text-sm border rounded transition-colors"
              :class="page === currentPage
                ? 'border-primary-500 bg-primary-500 text-white'
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800'"
            >
              {{ page }}
            </button>
          </div>
          
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
          <button
            @click="goToPage(totalPages)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            末页
          </button>
        </div>
      </div>
    </div>
    
    <!-- 列配置弹窗 -->
    <div 
      v-if="showColumnConfig"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showColumnConfig = false"
    >
      <div 
        class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-h-96 overflow-y-auto"
        @click.stop
      >
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">列配置</h3>
        
        <div class="space-y-3">
          <div
            v-for="column in allColumns"
            :key="column.key"
            class="flex items-center justify-between"
          >
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                :checked="column.visible"
                @change="toggleColumn(column)"
                class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span class="text-sm text-gray-700 dark:text-gray-300">{{ column.label }}</span>
            </label>
            
            <div class="flex items-center space-x-2">
              <input
                v-model="column.width"
                type="number"
                min="50"
                max="500"
                class="w-16 px-2 py-1 text-xs border border-gray-300 dark:border-gray-600 rounded"
                placeholder="宽度"
              />
            </div>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="resetColumns"
            class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            重置
          </button>
          <button
            @click="showColumnConfig = false"
            class="px-4 py-2 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
          >
            确定
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Table, 
  Settings, 
  RefreshCw, 
  ChevronUp, 
  ChevronDown, 
  Copy, 
  Eye, 
  Bookmark 
} from 'lucide-vue-next'

const appStore = useAppStore()

// 表格状态
const showColumnConfig = ref(false)
const selectedLogs = ref([])
const sortColumn = ref('timestamp')
const sortDirection = ref('desc')
const currentPage = ref(1)
const pageSize = ref('100')

// 模拟日志数据
const logs = ref([])
const totalLogs = computed(() => logs.value.length)

// 列配置
const allColumns = ref([
  { key: 'timestamp', label: '时间', visible: true, width: 150 },
  { key: 'level', label: '级别', visible: true, width: 80 },
  { key: 'source', label: '来源', visible: true, width: 120 },
  { key: 'message', label: '消息', visible: true, width: 400 },
  { key: 'user', label: '用户', visible: false, width: 100 },
  { key: 'ip', label: 'IP地址', visible: false, width: 120 },
  { key: 'responseTime', label: '响应时间', visible: false, width: 100 },
  { key: 'statusCode', label: '状态码', visible: false, width: 80 }
])

const visibleColumns = computed(() => {
  return allColumns.value.filter(col => col.visible)
})

// 排序和过滤
const sortedLogs = computed(() => {
  const sorted = [...logs.value].sort((a, b) => {
    const aVal = a[sortColumn.value]
    const bVal = b[sortColumn.value]
    
    if (sortDirection.value === 'asc') {
      return aVal > bVal ? 1 : -1
    } else {
      return aVal < bVal ? 1 : -1
    }
  })
  
  return sorted
})

const visibleLogs = computed(() => {
  return sortedLogs.value
})

// 分页
const totalPages = computed(() => {
  return Math.ceil(totalLogs.value / parseInt(pageSize.value))
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * parseInt(pageSize.value)
  const end = start + parseInt(pageSize.value)
  return visibleLogs.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value
  
  // 显示当前页前后2页
  const start = Math.max(1, current - 2)
  const end = Math.min(total, current + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// 生成模拟数据
const generateMockLogs = () => {
  const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG']
  const sources = ['nginx', 'mysql', 'redis', 'api-server', 'auth-service']
  const users = ['admin', 'user1', 'user2', 'guest', null]
  const ips = ['*************', '*********', '***********', '************', null]
  const messages = [
    'Database connection established successfully',
    'User authentication failed',
    'Cache miss for key: user_session_12345',
    'API request processed successfully',
    'Memory usage exceeded threshold',
    'SSL certificate validation failed',
    'Backup process completed',
    'Rate limit exceeded',
    'Database query execution time: 1.2s',
    'User session expired'
  ]
  
  const mockLogs = []
  for (let i = 0; i < 1000; i++) {
    mockLogs.push({
      id: i + 1,
      timestamp: new Date(Date.now() - Math.random() * 86400000 * 7), // 过去7天
      level: levels[Math.floor(Math.random() * levels.length)],
      source: sources[Math.floor(Math.random() * sources.length)],
      message: messages[Math.floor(Math.random() * messages.length)],
      user: users[Math.floor(Math.random() * users.length)],
      ip: ips[Math.floor(Math.random() * ips.length)],
      responseTime: Math.random() > 0.5 ? Math.floor(Math.random() * 2000) + 50 : null,
      statusCode: Math.random() > 0.3 ? [200, 201, 400, 401, 404, 500][Math.floor(Math.random() * 6)] : null
    })
  }
  
  logs.value = mockLogs
}

const handleSort = (column) => {
  if (sortColumn.value === column) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = column
    sortDirection.value = 'desc'
  }
}

const selectLog = (log) => {
  const index = selectedLogs.value.indexOf(log.id)
  if (index > -1) {
    selectedLogs.value.splice(index, 1)
  } else {
    selectedLogs.value.push(log.id)
  }
}

const showLogDetails = (log) => {
  appStore.addNotification({
    type: 'info',
    message: `查看日志详情: ${log.id}`
  })
}

const copyLog = (log) => {
  const text = `${formatTimestamp(log.timestamp)} [${log.level}] ${log.source}: ${log.message}`
  navigator.clipboard.writeText(text)
  appStore.addNotification({
    type: 'success',
    message: '日志已复制到剪贴板'
  })
}

const showContext = (log) => {
  appStore.addNotification({
    type: 'info',
    message: `查看上下文: ${log.id}`
  })
}

const addBookmark = (log) => {
  appStore.addNotification({
    type: 'success',
    message: '书签已添加'
  })
}

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  }
}

const toggleColumnConfig = () => {
  showColumnConfig.value = !showColumnConfig.value
}

const toggleColumn = (column) => {
  column.visible = !column.visible
}

const resetColumns = () => {
  allColumns.value.forEach(col => {
    col.visible = ['timestamp', 'level', 'source', 'message'].includes(col.key)
  })
}

const refreshData = () => {
  generateMockLogs()
  appStore.addNotification({
    type: 'success',
    message: '数据已刷新'
  })
}

const getLevelClass = (level) => {
  const classes = {
    ERROR: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    WARN: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    INFO: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    DEBUG: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
  return classes[level] || classes.INFO
}

const getSourceStatusClass = (source) => {
  // 模拟不同来源的状态
  const statusMap = {
    nginx: 'bg-green-500',
    mysql: 'bg-blue-500',
    redis: 'bg-red-500',
    'api-server': 'bg-green-500',
    'auth-service': 'bg-yellow-500'
  }
  return statusMap[source] || 'bg-gray-500'
}

const getResponseTimeClass = (time) => {
  if (time > 1000) return 'text-red-600 font-medium'
  if (time > 500) return 'text-yellow-600 font-medium'
  return 'text-green-600'
}

const getStatusCodeClass = (code) => {
  if (code >= 500) return 'text-red-600 font-medium'
  if (code >= 400) return 'text-yellow-600 font-medium'
  if (code >= 200 && code < 300) return 'text-green-600'
  return 'text-gray-600'
}

const formatTimestamp = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm:ss.SSS')
}

onMounted(() => {
  generateMockLogs()
})
</script>

<style scoped>
.log-table {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-header {
  min-height: 60px;
}

.pagination {
  min-height: 60px;
}

/* 自定义滚动条 */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark .overflow-auto::-webkit-scrollbar-track {
  background: #374151;
}

.dark .overflow-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .overflow-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
