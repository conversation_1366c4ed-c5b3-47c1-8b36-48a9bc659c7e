<template>
  <div class="space-y-6">
    <!-- 模板管理头部 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">日志模板管理</h3>
        <div class="flex items-center space-x-4">
          <button
            @click="importTemplates"
            class="btn-secondary"
          >
            <component :is="Upload" class="w-4 h-4 mr-2" />
            导入模板
          </button>
          <button
            @click="showCreateTemplate = true"
            class="btn-primary"
          >
            <component :is="Plus" class="w-4 h-4 mr-2" />
            创建模板
          </button>
        </div>
      </div>
      
      <!-- 模板统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ templateStats.total }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">总模板数</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ templateStats.active }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">活跃模板</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ templateStats.matched }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">今日匹配</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ templateStats.accuracy }}%</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">匹配准确率</div>
        </div>
      </div>
    </div>
    
    <!-- 模板列表 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">模板列表</h4>
        <div class="flex items-center space-x-4">
          <select v-model="filterCategory" class="input w-32">
            <option value="">全部分类</option>
            <option value="web">Web服务</option>
            <option value="database">数据库</option>
            <option value="security">安全</option>
            <option value="system">系统</option>
          </select>
          <input
            v-model="searchTemplate"
            type="text"
            placeholder="搜索模板..."
            class="input w-48"
          />
        </div>
      </div>
      
      <div class="space-y-4">
        <div
          v-for="template in filteredTemplates"
          :key="template.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-3">
              <h5 class="font-medium text-gray-900 dark:text-white">{{ template.name }}</h5>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getCategoryClass(template.category)"
              >
                {{ getCategoryLabel(template.category) }}
              </span>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="template.enabled 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'"
              >
                {{ template.enabled ? '启用' : '禁用' }}
              </span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="testTemplate(template)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
                title="测试模板"
              >
                <component :is="TestTube" class="w-4 h-4" />
              </button>
              <button
                @click="toggleTemplate(template)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="template.enabled ? Pause : Play" class="w-4 h-4" />
              </button>
              <button
                @click="editTemplate(template)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="Edit" class="w-4 h-4" />
              </button>
              <button
                @click="deleteTemplate(template)"
                class="p-2 text-red-400 hover:text-red-600 rounded"
              >
                <component :is="Trash2" class="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ template.description }}</p>
          
          <!-- 模板规则 -->
          <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 mb-3">
            <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">匹配规则:</div>
            <code class="text-xs font-mono text-gray-600 dark:text-gray-400">{{ template.pattern }}</code>
          </div>
          
          <!-- 提取字段 -->
          <div class="mb-3">
            <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">提取字段:</div>
            <div class="flex flex-wrap gap-2">
              <span
                v-for="field in template.fields"
                :key="field"
                class="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded"
              >
                {{ field }}
              </span>
            </div>
          </div>
          
          <!-- 统计信息 -->
          <div class="flex items-center justify-between text-xs text-gray-500">
            <div class="flex items-center space-x-4">
              <span>匹配次数: {{ template.matchCount }}</span>
              <span>准确率: {{ template.accuracy }}%</span>
              <span>最后匹配: {{ formatTime(template.lastMatch) }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span>创建者: {{ template.creator }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 预定义模板库 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">预定义模板库</h4>
        <button
          @click="refreshTemplateLibrary"
          class="btn-secondary"
        >
          <component :is="RefreshCw" class="w-4 h-4 mr-2" />
          刷新
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="preset in presetTemplates"
          :key="preset.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-center justify-between mb-2">
            <h5 class="font-medium text-gray-900 dark:text-white">{{ preset.name }}</h5>
            <span
              class="px-2 py-1 text-xs font-medium rounded-full"
              :class="getCategoryClass(preset.category)"
            >
              {{ getCategoryLabel(preset.category) }}
            </span>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ preset.description }}</p>
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-500">
              <span>下载: {{ preset.downloads }}</span>
              <span class="ml-2">评分: {{ preset.rating }}/5</span>
            </div>
            <button
              @click="installTemplate(preset)"
              class="text-sm text-primary-600 hover:text-primary-700"
            >
              安装
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 模板测试器 -->
    <div v-if="showTemplateTester" class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">模板测试器</h4>
        <button
          @click="showTemplateTester = false"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
        >
          <component :is="X" class="w-4 h-4" />
        </button>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 测试输入 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            测试日志
          </label>
          <textarea
            v-model="testLogInput"
            class="input h-32"
            placeholder="粘贴要测试的日志内容..."
          ></textarea>
          <button
            @click="runTemplateTest"
            class="btn-primary mt-3"
          >
            <component :is="Play" class="w-4 h-4 mr-2" />
            运行测试
          </button>
        </div>
        
        <!-- 测试结果 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            提取结果
          </label>
          <div class="h-32 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg overflow-y-auto">
            <div v-if="testResults.length === 0" class="text-gray-500 text-sm">
              运行测试以查看结果
            </div>
            <div v-else class="space-y-2">
              <div
                v-for="(result, index) in testResults"
                :key="index"
                class="text-sm"
              >
                <span class="font-medium text-gray-900 dark:text-white">{{ result.field }}:</span>
                <span class="text-gray-600 dark:text-gray-400 ml-2">{{ result.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Plus, 
  Upload, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  TestTube, 
  RefreshCw, 
  X 
} from 'lucide-vue-next'

const appStore = useAppStore()

const showCreateTemplate = ref(false)
const showTemplateTester = ref(false)
const filterCategory = ref('')
const searchTemplate = ref('')
const testLogInput = ref('')
const testResults = ref([])

const templateStats = ref({
  total: 24,
  active: 18,
  matched: 1247,
  accuracy: 94.5
})

const templates = ref([
  {
    id: 1,
    name: 'Nginx访问日志',
    description: '解析Nginx标准访问日志格式',
    category: 'web',
    pattern: '^(?<ip>\\S+) - (?<user>\\S+) \\[(?<timestamp>[^\\]]+)\\] "(?<method>\\S+) (?<path>\\S+) (?<protocol>\\S+)" (?<status>\\d+) (?<size>\\d+)',
    fields: ['ip', 'user', 'timestamp', 'method', 'path', 'protocol', 'status', 'size'],
    enabled: true,
    matchCount: 15420,
    accuracy: 98.2,
    lastMatch: new Date(Date.now() - 300000),
    creator: '张三'
  },
  {
    id: 2,
    name: 'MySQL错误日志',
    description: '解析MySQL数据库错误日志',
    category: 'database',
    pattern: '^(?<timestamp>\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d+Z) (?<level>\\w+) (?<message>.*)',
    fields: ['timestamp', 'level', 'message'],
    enabled: true,
    matchCount: 892,
    accuracy: 95.7,
    lastMatch: new Date(Date.now() - 600000),
    creator: '李四'
  },
  {
    id: 3,
    name: '安全审计日志',
    description: '解析系统安全审计日志',
    category: 'security',
    pattern: '^(?<timestamp>[^\\s]+) (?<hostname>\\S+) (?<process>\\S+): (?<event_type>\\w+) (?<user>\\S+) (?<action>.*)',
    fields: ['timestamp', 'hostname', 'process', 'event_type', 'user', 'action'],
    enabled: false,
    matchCount: 234,
    accuracy: 92.1,
    lastMatch: new Date(Date.now() - 1800000),
    creator: '王五'
  }
])

const presetTemplates = ref([
  {
    id: 101,
    name: 'Apache访问日志',
    description: 'Apache HTTP服务器标准访问日志格式',
    category: 'web',
    downloads: 1520,
    rating: 4.8
  },
  {
    id: 102,
    name: 'PostgreSQL日志',
    description: 'PostgreSQL数据库日志解析模板',
    category: 'database',
    downloads: 890,
    rating: 4.6
  },
  {
    id: 103,
    name: 'Docker容器日志',
    description: 'Docker容器运行时日志格式',
    category: 'system',
    downloads: 2340,
    rating: 4.9
  },
  {
    id: 104,
    name: 'Kubernetes事件日志',
    description: 'Kubernetes集群事件日志解析',
    category: 'system',
    downloads: 1680,
    rating: 4.7
  },
  {
    id: 105,
    name: 'AWS CloudTrail',
    description: 'AWS CloudTrail审计日志格式',
    category: 'security',
    downloads: 756,
    rating: 4.5
  },
  {
    id: 106,
    name: 'Redis日志',
    description: 'Redis缓存服务器日志解析',
    category: 'database',
    downloads: 432,
    rating: 4.4
  }
])

const filteredTemplates = computed(() => {
  let filtered = templates.value
  
  if (filterCategory.value) {
    filtered = filtered.filter(t => t.category === filterCategory.value)
  }
  
  if (searchTemplate.value) {
    const search = searchTemplate.value.toLowerCase()
    filtered = filtered.filter(t => 
      t.name.toLowerCase().includes(search) ||
      t.description.toLowerCase().includes(search)
    )
  }
  
  return filtered
})

const getCategoryClass = (category) => {
  const classes = {
    web: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    database: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    security: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    system: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
  }
  return classes[category] || classes.system
}

const getCategoryLabel = (category) => {
  const labels = {
    web: 'Web服务',
    database: '数据库',
    security: '安全',
    system: '系统'
  }
  return labels[category] || '其他'
}

const toggleTemplate = (template) => {
  template.enabled = !template.enabled
  appStore.addNotification({
    type: 'success',
    message: `模板 "${template.name}" 已${template.enabled ? '启用' : '禁用'}`
  })
}

const editTemplate = (template) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑模板: ${template.name}`
  })
}

const deleteTemplate = (template) => {
  if (confirm(`确定要删除模板 "${template.name}" 吗？`)) {
    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '模板已删除'
      })
    }
  }
}

const testTemplate = (template) => {
  showTemplateTester.value = true
  testLogInput.value = getExampleLog(template.category)
}

const getExampleLog = (category) => {
  const examples = {
    web: '************* - - [15/Jan/2024:10:30:45 +0000] "GET /api/users HTTP/1.1" 200 1234',
    database: '2024-01-15T10:30:45.123Z ERROR Connection timeout after 30 seconds',
    security: '2024-01-15 server01 sshd: FAILED_LOGIN user root from *************',
    system: '2024-01-15T10:30:45Z container_name: Application started successfully'
  }
  return examples[category] || examples.system
}

const runTemplateTest = () => {
  // 模拟模板测试
  testResults.value = [
    { field: 'ip', value: '*************' },
    { field: 'timestamp', value: '15/Jan/2024:10:30:45 +0000' },
    { field: 'method', value: 'GET' },
    { field: 'path', value: '/api/users' },
    { field: 'status', value: '200' }
  ]
  
  appStore.addNotification({
    type: 'success',
    message: '模板测试完成'
  })
}

const installTemplate = (preset) => {
  const newTemplate = {
    id: Date.now(),
    name: preset.name,
    description: preset.description,
    category: preset.category,
    pattern: '^(?<timestamp>\\S+) (?<level>\\w+) (?<message>.*)',
    fields: ['timestamp', 'level', 'message'],
    enabled: true,
    matchCount: 0,
    accuracy: 0,
    lastMatch: new Date(),
    creator: '系统'
  }
  
  templates.value.push(newTemplate)
  
  appStore.addNotification({
    type: 'success',
    message: `模板 "${preset.name}" 安装成功`
  })
}

const importTemplates = () => {
  appStore.addNotification({
    type: 'info',
    message: '模板导入功能开发中'
  })
}

const refreshTemplateLibrary = () => {
  appStore.addNotification({
    type: 'success',
    message: '模板库已刷新'
  })
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}
</script>
