<template>
  <div class="space-y-6">
    <!-- 性能概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">性能监控</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="getPerformanceStatusColor(overallPerformance.status)"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ getPerformanceStatusText(overallPerformance.status) }}
            </span>
          </div>
          <button
            @click="runPerformanceAnalysis"
            class="btn-primary"
            :disabled="isAnalyzing"
          >
            <component :is="isAnalyzing ? Loader : Zap" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isAnalyzing }" />
            {{ isAnalyzing ? '分析中...' : '性能分析' }}
          </button>
        </div>
      </div>
      
      <!-- 性能指标 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ performanceMetrics.queryTime }}ms</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">平均查询时间</div>
          <div class="text-xs text-gray-500 mt-1">
            <component :is="performanceMetrics.queryTrend > 0 ? TrendingUp : TrendingDown" class="w-3 h-3 inline mr-1" />
            {{ Math.abs(performanceMetrics.queryTrend) }}%
          </div>
        </div>
        
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ performanceMetrics.throughput }}/s</div>
          <div class="text-sm text-green-600 dark:text-green-400">处理吞吐量</div>
          <div class="text-xs text-gray-500 mt-1">
            <component :is="performanceMetrics.throughputTrend > 0 ? TrendingUp : TrendingDown" class="w-3 h-3 inline mr-1" />
            {{ Math.abs(performanceMetrics.throughputTrend) }}%
          </div>
        </div>
        
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ performanceMetrics.memoryUsage }}%</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">内存使用率</div>
          <div class="text-xs text-gray-500 mt-1">
            <component :is="performanceMetrics.memoryTrend > 0 ? TrendingUp : TrendingDown" class="w-3 h-3 inline mr-1" />
            {{ Math.abs(performanceMetrics.memoryTrend) }}%
          </div>
        </div>
        
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ performanceMetrics.errorRate }}%</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">错误率</div>
          <div class="text-xs text-gray-500 mt-1">
            <component :is="performanceMetrics.errorTrend > 0 ? TrendingUp : TrendingDown" class="w-3 h-3 inline mr-1" />
            {{ Math.abs(performanceMetrics.errorTrend) }}%
          </div>
        </div>
      </div>
    </div>
    
    <!-- 性能图表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 响应时间趋势 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">响应时间趋势</h4>
        <div class="h-64">
          <PerformanceTrendChart :data="responseTrendData" />
        </div>
      </div>
      
      <!-- 资源使用情况 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">资源使用情况</h4>
        <div class="h-64">
          <ResourceUsageChart :data="resourceUsageData" />
        </div>
      </div>
    </div>
    
    <!-- 性能瓶颈分析 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">性能瓶颈分析</h4>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 慢查询分析 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">慢查询TOP 5</h5>
          <div class="space-y-3">
            <div
              v-for="query in slowQueries"
              :key="query.id"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 dark:text-white truncate">{{ query.query }}</div>
                <div class="text-xs text-gray-500">执行次数: {{ query.count }}</div>
              </div>
              <div class="text-right ml-4">
                <div class="text-sm font-medium text-red-600">{{ query.avgTime }}ms</div>
                <div class="text-xs text-gray-500">平均时间</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 资源热点 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">资源热点</h5>
          <div class="space-y-3">
            <div
              v-for="hotspot in resourceHotspots"
              :key="hotspot.name"
              class="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ hotspot.name }}</span>
                <span 
                  class="text-sm font-medium"
                  :class="hotspot.usage > 80 ? 'text-red-600' : hotspot.usage > 60 ? 'text-yellow-600' : 'text-green-600'"
                >
                  {{ hotspot.usage }}%
                </span>
              </div>
              <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div 
                  class="h-2 rounded-full transition-all duration-300"
                  :class="hotspot.usage > 80 ? 'bg-red-500' : hotspot.usage > 60 ? 'bg-yellow-500' : 'bg-green-500'"
                  :style="{ width: `${hotspot.usage}%` }"
                ></div>
              </div>
              <div class="text-xs text-gray-500 mt-1">{{ hotspot.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 优化建议 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">优化建议</h4>
      
      <div class="space-y-4">
        <div
          v-for="suggestion in optimizationSuggestions"
          :key="suggestion.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-start space-x-3">
            <component 
              :is="getSuggestionIcon(suggestion.type)" 
              class="w-5 h-5 mt-0.5 flex-shrink-0"
              :class="getSuggestionIconColor(suggestion.priority)"
            />
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-gray-900 dark:text-white">{{ suggestion.title }}</h5>
                <div class="flex items-center space-x-2">
                  <span
                    class="px-2 py-1 text-xs font-medium rounded-full"
                    :class="getPriorityClass(suggestion.priority)"
                  >
                    {{ getPriorityLabel(suggestion.priority) }}
                  </span>
                  <span
                    class="px-2 py-1 text-xs font-medium rounded-full"
                    :class="getImpactClass(suggestion.impact)"
                  >
                    {{ suggestion.impact }}
                  </span>
                </div>
              </div>
              <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ suggestion.description }}</p>
              
              <!-- 实施步骤 -->
              <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-3">
                <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">实施步骤:</div>
                <ol class="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                  <li v-for="(step, index) in suggestion.steps" :key="index">
                    {{ index + 1 }}. {{ step }}
                  </li>
                </ol>
              </div>
              
              <div class="flex items-center justify-between mt-3">
                <div class="text-xs text-gray-500">
                  <span>预期收益: {{ suggestion.expectedBenefit }}</span>
                  <span class="ml-4">实施难度: {{ suggestion.difficulty }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <button
                    @click="applySuggestion(suggestion)"
                    class="text-xs text-primary-600 hover:text-primary-700"
                  >
                    应用建议
                  </button>
                  <button
                    @click="dismissSuggestion(suggestion)"
                    class="text-xs text-gray-500 hover:text-gray-700"
                  >
                    忽略
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 性能基准测试 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">性能基准测试</h4>
        <button
          @click="runBenchmark"
          class="btn-secondary"
          :disabled="isBenchmarking"
        >
          <component :is="isBenchmarking ? Loader : Target" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isBenchmarking }" />
          {{ isBenchmarking ? '测试中...' : '运行测试' }}
        </button>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div
          v-for="benchmark in benchmarkResults"
          :key="benchmark.name"
          class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
        >
          <div class="text-center">
            <div class="text-lg font-bold text-gray-900 dark:text-white">{{ benchmark.score }}</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">{{ benchmark.name }}</div>
            <div 
              class="text-xs mt-1"
              :class="benchmark.status === 'good' ? 'text-green-600' : 
                      benchmark.status === 'warning' ? 'text-yellow-600' : 'text-red-600'"
            >
              {{ benchmark.status === 'good' ? '优秀' : 
                  benchmark.status === 'warning' ? '一般' : '需要优化' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { 
  Zap, 
  Loader, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertTriangle,
  Settings,
  Database,
  Server
} from 'lucide-vue-next'

// 导入图表组件
import PerformanceTrendChart from '@/components/charts/PerformanceTrendChart.vue'
import ResourceUsageChart from '@/components/charts/ResourceUsageChart.vue'

const appStore = useAppStore()

const isAnalyzing = ref(false)
const isBenchmarking = ref(false)

const overallPerformance = reactive({
  status: 'good', // good, warning, critical
  score: 85
})

const performanceMetrics = reactive({
  queryTime: 245,
  queryTrend: -5.2,
  throughput: 1247,
  throughputTrend: 8.5,
  memoryUsage: 68,
  memoryTrend: 2.1,
  errorRate: 2.3,
  errorTrend: -1.8
})

const responseTrendData = ref([])
const resourceUsageData = ref([])

const slowQueries = ref([
  {
    id: 1,
    query: 'SELECT * FROM logs WHERE message LIKE "%error%" ORDER BY timestamp DESC',
    avgTime: 1250,
    count: 234
  },
  {
    id: 2,
    query: 'SELECT COUNT(*) FROM logs GROUP BY source, level',
    avgTime: 890,
    count: 156
  },
  {
    id: 3,
    query: 'SELECT * FROM logs WHERE timestamp BETWEEN ? AND ? AND level = "error"',
    avgTime: 675,
    count: 89
  },
  {
    id: 4,
    query: 'SELECT DISTINCT user_id FROM logs WHERE level = "error"',
    avgTime: 543,
    count: 67
  },
  {
    id: 5,
    query: 'SELECT * FROM logs WHERE source = "nginx" AND response_time > 1000',
    avgTime: 432,
    count: 45
  }
])

const resourceHotspots = ref([
  {
    name: 'CPU使用率',
    usage: 75,
    description: '查询处理占用较高CPU资源'
  },
  {
    name: '内存使用率',
    usage: 68,
    description: '日志缓存占用大量内存'
  },
  {
    name: '磁盘I/O',
    usage: 45,
    description: '日志读写操作正常'
  },
  {
    name: '网络带宽',
    usage: 32,
    description: '网络传输负载较低'
  }
])

const optimizationSuggestions = ref([
  {
    id: 1,
    type: 'database',
    priority: 'high',
    impact: '高',
    title: '优化慢查询性能',
    description: '检测到多个慢查询，建议添加索引和优化查询语句',
    expectedBenefit: '查询速度提升60%',
    difficulty: '中等',
    steps: [
      '为timestamp字段添加索引',
      '优化LIKE查询使用全文索引',
      '考虑分区表策略',
      '调整查询缓存大小'
    ]
  },
  {
    id: 2,
    type: 'memory',
    priority: 'medium',
    impact: '中',
    title: '内存使用优化',
    description: '内存使用率偏高，建议优化缓存策略',
    expectedBenefit: '内存使用降低30%',
    difficulty: '低',
    steps: [
      '调整日志缓存大小',
      '实施LRU缓存策略',
      '定期清理过期缓存',
      '优化内存分配'
    ]
  },
  {
    id: 3,
    type: 'performance',
    priority: 'medium',
    impact: '中',
    title: '查询结果分页优化',
    description: '大结果集查询影响性能，建议实施更好的分页策略',
    expectedBenefit: '响应时间减少40%',
    difficulty: '低',
    steps: [
      '实施游标分页',
      '限制单次查询结果数量',
      '添加查询超时机制',
      '优化前端分页组件'
    ]
  }
])

const benchmarkResults = ref([
  {
    name: '查询性能',
    score: 85,
    status: 'good'
  },
  {
    name: '并发处理',
    score: 72,
    status: 'warning'
  },
  {
    name: '资源效率',
    score: 91,
    status: 'good'
  }
])

let metricsInterval = null

const getPerformanceStatusColor = (status) => {
  const colors = {
    good: 'bg-green-500',
    warning: 'bg-yellow-500',
    critical: 'bg-red-500'
  }
  return colors[status] || colors.good
}

const getPerformanceStatusText = (status) => {
  const texts = {
    good: '性能良好',
    warning: '性能警告',
    critical: '性能严重'
  }
  return texts[status] || '未知状态'
}

const getSuggestionIcon = (type) => {
  const icons = {
    database: Database,
    memory: Server,
    performance: Zap,
    security: AlertTriangle
  }
  return icons[type] || Settings
}

const getSuggestionIconColor = (priority) => {
  const colors = {
    high: 'text-red-500',
    medium: 'text-yellow-500',
    low: 'text-green-500'
  }
  return colors[priority] || colors.medium
}

const getPriorityClass = (priority) => {
  const classes = {
    high: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[priority] || classes.medium
}

const getPriorityLabel = (priority) => {
  const labels = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  }
  return labels[priority] || '中优先级'
}

const getImpactClass = (impact) => {
  const classes = {
    '高': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    '中': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    '低': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[impact] || classes['中']
}

const runPerformanceAnalysis = async () => {
  isAnalyzing.value = true
  
  try {
    // 模拟性能分析过程
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新性能指标
    performanceMetrics.queryTime = Math.floor(Math.random() * 200) + 150
    performanceMetrics.throughput = Math.floor(Math.random() * 500) + 1000
    performanceMetrics.memoryUsage = Math.floor(Math.random() * 30) + 50
    performanceMetrics.errorRate = Math.random() * 5
    
    appStore.addNotification({
      type: 'success',
      message: '性能分析完成，发现了多个优化机会'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '性能分析失败，请重试'
    })
  } finally {
    isAnalyzing.value = false
  }
}

const runBenchmark = async () => {
  isBenchmarking.value = true
  
  try {
    // 模拟基准测试
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    // 更新基准测试结果
    benchmarkResults.value.forEach(benchmark => {
      benchmark.score = Math.floor(Math.random() * 40) + 60
      benchmark.status = benchmark.score > 80 ? 'good' : 
                        benchmark.score > 60 ? 'warning' : 'critical'
    })
    
    appStore.addNotification({
      type: 'success',
      message: '基准测试完成'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '基准测试失败，请重试'
    })
  } finally {
    isBenchmarking.value = false
  }
}

const applySuggestion = (suggestion) => {
  appStore.addNotification({
    type: 'success',
    message: `正在应用优化建议: ${suggestion.title}`
  })
  
  // 从建议列表中移除
  const index = optimizationSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    optimizationSuggestions.value.splice(index, 1)
  }
}

const dismissSuggestion = (suggestion) => {
  const index = optimizationSuggestions.value.findIndex(s => s.id === suggestion.id)
  if (index > -1) {
    optimizationSuggestions.value.splice(index, 1)
    appStore.addNotification({
      type: 'info',
      message: '建议已忽略'
    })
  }
}

const updateMetrics = () => {
  // 模拟实时指标更新
  const now = new Date()
  
  // 更新响应时间趋势数据
  responseTrendData.value.push({
    time: now.toISOString(),
    value: performanceMetrics.queryTime + (Math.random() - 0.5) * 50
  })
  
  if (responseTrendData.value.length > 50) {
    responseTrendData.value.shift()
  }
  
  // 更新资源使用数据
  resourceUsageData.value = [
    { name: 'CPU', value: performanceMetrics.memoryUsage + (Math.random() - 0.5) * 10 },
    { name: '内存', value: performanceMetrics.memoryUsage },
    { name: '磁盘', value: 45 + (Math.random() - 0.5) * 10 },
    { name: '网络', value: 32 + (Math.random() - 0.5) * 10 }
  ]
}

onMounted(() => {
  // 初始化图表数据
  for (let i = 0; i < 20; i++) {
    updateMetrics()
  }
  
  // 启动实时更新
  metricsInterval = setInterval(updateMetrics, 5000)
})

onUnmounted(() => {
  if (metricsInterval) {
    clearInterval(metricsInterval)
  }
})
</script>
