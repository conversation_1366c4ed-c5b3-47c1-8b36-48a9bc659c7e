<template>
  <div class="card p-6">
    <div class="flex items-center justify-between mb-6">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white">查询构建器</h3>
      <div class="flex items-center space-x-4">
        <button
          @click="showQueryHistory = !showQueryHistory"
          class="btn-secondary"
        >
          <component :is="History" class="w-4 h-4 mr-2" />
          查询历史
        </button>
        <button
          @click="saveQuery"
          class="btn-primary"
        >
          <component :is="Save" class="w-4 h-4 mr-2" />
          保存查询
        </button>
      </div>
    </div>
    
    <!-- 查询模式切换 -->
    <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-6">
      <button
        v-for="mode in queryModes"
        :key="mode.key"
        @click="currentMode = mode.key"
        class="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors"
        :class="currentMode === mode.key
          ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'"
      >
        <component :is="mode.icon" class="w-4 h-4 mr-2 inline" />
        {{ mode.label }}
      </button>
    </div>
    
    <!-- 可视化查询构建器 -->
    <div v-if="currentMode === 'visual'" class="space-y-4">
      <!-- 查询条件组 -->
      <div
        v-for="(group, groupIndex) in queryGroups"
        :key="groupIndex"
        class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-700 dark:text-gray-300">条件组 {{ groupIndex + 1 }}</span>
            <select
              v-model="group.operator"
              class="input w-20 text-sm"
            >
              <option value="AND">AND</option>
              <option value="OR">OR</option>
            </select>
          </div>
          <button
            @click="removeGroup(groupIndex)"
            class="p-1 text-red-400 hover:text-red-600 rounded"
          >
            <component :is="Trash2" class="w-4 h-4" />
          </button>
        </div>
        
        <!-- 查询条件 -->
        <div class="space-y-3">
          <div
            v-for="(condition, conditionIndex) in group.conditions"
            :key="conditionIndex"
            class="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <!-- 字段选择 -->
            <select
              v-model="condition.field"
              class="input w-32"
            >
              <option value="">选择字段</option>
              <option v-for="field in availableFields" :key="field.key" :value="field.key">
                {{ field.label }}
              </option>
            </select>
            
            <!-- 操作符选择 -->
            <select
              v-model="condition.operator"
              class="input w-32"
            >
              <option v-for="op in getOperators(condition.field)" :key="op.value" :value="op.value">
                {{ op.label }}
              </option>
            </select>
            
            <!-- 值输入 -->
            <input
              v-if="getFieldType(condition.field) === 'text'"
              v-model="condition.value"
              type="text"
              placeholder="输入值..."
              class="input flex-1"
            />
            <input
              v-else-if="getFieldType(condition.field) === 'number'"
              v-model="condition.value"
              type="number"
              placeholder="输入数值..."
              class="input flex-1"
            />
            <input
              v-else-if="getFieldType(condition.field) === 'datetime'"
              v-model="condition.value"
              type="datetime-local"
              class="input flex-1"
            />
            <select
              v-else-if="getFieldType(condition.field) === 'select'"
              v-model="condition.value"
              class="input flex-1"
            >
              <option value="">选择值</option>
              <option v-for="option in getFieldOptions(condition.field)" :key="option" :value="option">
                {{ option }}
              </option>
            </select>
            
            <!-- 删除条件 -->
            <button
              @click="removeCondition(groupIndex, conditionIndex)"
              class="p-1 text-red-400 hover:text-red-600 rounded"
            >
              <component :is="X" class="w-4 h-4" />
            </button>
          </div>
          
          <!-- 添加条件 -->
          <button
            @click="addCondition(groupIndex)"
            class="w-full p-2 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
          >
            <component :is="Plus" class="w-4 h-4 mr-2 inline" />
            添加条件
          </button>
        </div>
      </div>
      
      <!-- 添加条件组 -->
      <button
        @click="addGroup"
        class="w-full p-3 border-2 border-dashed border-primary-300 dark:border-primary-600 rounded-lg text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 hover:border-primary-400 dark:hover:border-primary-500 transition-colors"
      >
        <component :is="Plus" class="w-4 h-4 mr-2 inline" />
        添加条件组
      </button>
    </div>
    
    <!-- SQL查询模式 -->
    <div v-if="currentMode === 'sql'" class="space-y-4">
      <div class="relative">
        <textarea
          v-model="sqlQuery"
          placeholder="输入SQL查询语句..."
          class="input h-32 font-mono text-sm"
        ></textarea>
        <div class="absolute top-2 right-2 flex space-x-2">
          <button
            @click="formatSQL"
            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="格式化SQL"
          >
            <component :is="Code" class="w-4 h-4" />
          </button>
          <button
            @click="validateSQL"
            class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
            title="验证SQL"
          >
            <component :is="CheckCircle" class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <!-- SQL语法提示 -->
      <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <h5 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">SQL语法提示</h5>
        <div class="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <div>• 基本查询: SELECT * FROM logs WHERE level = 'error'</div>
          <div>• 时间范围: WHERE timestamp BETWEEN '2024-01-01' AND '2024-01-31'</div>
          <div>• 模糊匹配: WHERE message LIKE '%timeout%'</div>
          <div>• 正则表达式: WHERE message REGEXP '^Error.*database'</div>
        </div>
      </div>
    </div>
    
    <!-- 自然语言查询 -->
    <div v-if="currentMode === 'natural'" class="space-y-4">
      <div class="relative">
        <input
          v-model="naturalQuery"
          type="text"
          placeholder="用自然语言描述你要查找的日志，例如：'显示昨天的所有错误日志'"
          class="input pr-12"
        />
        <button
          @click="processNaturalQuery"
          class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
        >
          <component :is="Zap" class="w-4 h-4" />
        </button>
      </div>
      
      <!-- 自然语言示例 -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div
          v-for="example in naturalExamples"
          :key="example.text"
          @click="naturalQuery = example.text"
          class="p-3 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <div class="text-sm font-medium text-gray-900 dark:text-white">{{ example.text }}</div>
          <div class="text-xs text-gray-500 mt-1">{{ example.description }}</div>
        </div>
      </div>
    </div>
    
    <!-- 查询预览和执行 -->
    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">查询预览</h4>
        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">预计结果: {{ estimatedResults }} 条</span>
          <button
            @click="executeQuery"
            class="btn-primary"
            :disabled="!isQueryValid"
          >
            <component :is="Play" class="w-4 h-4 mr-2" />
            执行查询
          </button>
        </div>
      </div>
      
      <!-- 生成的查询语句 -->
      <div class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div class="text-xs text-gray-500 mb-2">生成的查询语句:</div>
        <code class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ generatedQuery }}</code>
      </div>
    </div>
    
    <!-- 查询历史侧边栏 -->
    <div
      v-if="showQueryHistory"
      class="fixed inset-y-0 right-0 w-96 bg-white dark:bg-gray-800 shadow-xl z-50 overflow-y-auto"
    >
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">查询历史</h3>
          <button
            @click="showQueryHistory = false"
            class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
          >
            <component :is="X" class="w-5 h-5" />
          </button>
        </div>
        
        <div class="space-y-4">
          <div
            v-for="query in queryHistory"
            :key="query.id"
            @click="loadQuery(query)"
            class="p-4 border border-gray-200 dark:border-gray-700 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium text-gray-900 dark:text-white">{{ query.name }}</span>
              <span class="text-xs text-gray-500">{{ formatTime(query.timestamp) }}</span>
            </div>
            <div class="text-xs text-gray-600 dark:text-gray-400 font-mono bg-gray-100 dark:bg-gray-800 rounded p-2">
              {{ query.query }}
            </div>
            <div class="text-xs text-gray-500 mt-2">
              结果: {{ query.resultCount }} 条 • 执行时间: {{ query.executionTime }}ms
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  History, 
  Save, 
  Search, 
  Code, 
  Zap, 
  Plus, 
  X, 
  Trash2, 
  Play, 
  CheckCircle 
} from 'lucide-vue-next'

const appStore = useAppStore()

const currentMode = ref('visual')
const showQueryHistory = ref(false)
const sqlQuery = ref('')
const naturalQuery = ref('')

const queryModes = [
  { key: 'visual', label: '可视化构建', icon: Search },
  { key: 'sql', label: 'SQL查询', icon: Code },
  { key: 'natural', label: '自然语言', icon: Zap }
]

const queryGroups = ref([
  {
    operator: 'AND',
    conditions: [
      { field: '', operator: 'equals', value: '' }
    ]
  }
])

const availableFields = [
  { key: 'timestamp', label: '时间戳', type: 'datetime' },
  { key: 'level', label: '日志级别', type: 'select', options: ['error', 'warn', 'info', 'debug', 'trace'] },
  { key: 'source', label: '来源', type: 'select', options: ['nginx', 'apache', 'mysql', 'redis'] },
  { key: 'service', label: '服务', type: 'select', options: ['web', 'api', 'database', 'cache'] },
  { key: 'message', label: '消息', type: 'text' },
  { key: 'user_id', label: '用户ID', type: 'text' },
  { key: 'ip_address', label: 'IP地址', type: 'text' },
  { key: 'response_time', label: '响应时间', type: 'number' }
]

const naturalExamples = [
  { text: '显示昨天的所有错误日志', description: '查找昨天发生的错误级别日志' },
  { text: '查找包含"timeout"的日志', description: '搜索消息中包含超时关键词的日志' },
  { text: '显示nginx服务的警告日志', description: '过滤nginx来源的警告级别日志' },
  { text: '查找响应时间超过1秒的请求', description: '查找性能异常的请求日志' }
]

const queryHistory = ref([
  {
    id: 1,
    name: '生产环境错误查询',
    query: 'SELECT * FROM logs WHERE level = "error" AND timestamp > "2024-01-15"',
    timestamp: new Date(Date.now() - 3600000),
    resultCount: 234,
    executionTime: 45
  },
  {
    id: 2,
    name: 'API超时分析',
    query: 'SELECT * FROM logs WHERE message LIKE "%timeout%" AND service = "api"',
    timestamp: new Date(Date.now() - 7200000),
    resultCount: 89,
    executionTime: 32
  }
])

const getOperators = (field) => {
  const fieldType = getFieldType(field)
  
  const operators = {
    text: [
      { value: 'equals', label: '等于' },
      { value: 'not_equals', label: '不等于' },
      { value: 'contains', label: '包含' },
      { value: 'not_contains', label: '不包含' },
      { value: 'starts_with', label: '开始于' },
      { value: 'ends_with', label: '结束于' },
      { value: 'regex', label: '正则匹配' }
    ],
    number: [
      { value: 'equals', label: '等于' },
      { value: 'not_equals', label: '不等于' },
      { value: 'greater_than', label: '大于' },
      { value: 'less_than', label: '小于' },
      { value: 'greater_equal', label: '大于等于' },
      { value: 'less_equal', label: '小于等于' },
      { value: 'between', label: '介于' }
    ],
    datetime: [
      { value: 'equals', label: '等于' },
      { value: 'not_equals', label: '不等于' },
      { value: 'after', label: '之后' },
      { value: 'before', label: '之前' },
      { value: 'between', label: '介于' }
    ],
    select: [
      { value: 'equals', label: '等于' },
      { value: 'not_equals', label: '不等于' },
      { value: 'in', label: '包含于' },
      { value: 'not_in', label: '不包含于' }
    ]
  }
  
  return operators[fieldType] || operators.text
}

const getFieldType = (fieldKey) => {
  const field = availableFields.find(f => f.key === fieldKey)
  return field?.type || 'text'
}

const getFieldOptions = (fieldKey) => {
  const field = availableFields.find(f => f.key === fieldKey)
  return field?.options || []
}

const addGroup = () => {
  queryGroups.value.push({
    operator: 'AND',
    conditions: [
      { field: '', operator: 'equals', value: '' }
    ]
  })
}

const removeGroup = (index) => {
  if (queryGroups.value.length > 1) {
    queryGroups.value.splice(index, 1)
  }
}

const addCondition = (groupIndex) => {
  queryGroups.value[groupIndex].conditions.push({
    field: '',
    operator: 'equals',
    value: ''
  })
}

const removeCondition = (groupIndex, conditionIndex) => {
  const group = queryGroups.value[groupIndex]
  if (group.conditions.length > 1) {
    group.conditions.splice(conditionIndex, 1)
  }
}

const generatedQuery = computed(() => {
  if (currentMode.value === 'sql') {
    return sqlQuery.value
  } else if (currentMode.value === 'natural') {
    return `自然语言查询: ${naturalQuery.value}`
  } else {
    // 生成可视化查询的SQL
    return generateSQLFromVisual()
  }
})

const generateSQLFromVisual = () => {
  const conditions = []
  
  queryGroups.value.forEach((group, groupIndex) => {
    const groupConditions = []
    
    group.conditions.forEach(condition => {
      if (condition.field && condition.value) {
        let conditionStr = ''
        
        switch (condition.operator) {
          case 'equals':
            conditionStr = `${condition.field} = '${condition.value}'`
            break
          case 'not_equals':
            conditionStr = `${condition.field} != '${condition.value}'`
            break
          case 'contains':
            conditionStr = `${condition.field} LIKE '%${condition.value}%'`
            break
          case 'not_contains':
            conditionStr = `${condition.field} NOT LIKE '%${condition.value}%'`
            break
          case 'starts_with':
            conditionStr = `${condition.field} LIKE '${condition.value}%'`
            break
          case 'ends_with':
            conditionStr = `${condition.field} LIKE '%${condition.value}'`
            break
          case 'regex':
            conditionStr = `${condition.field} REGEXP '${condition.value}'`
            break
          case 'greater_than':
            conditionStr = `${condition.field} > ${condition.value}`
            break
          case 'less_than':
            conditionStr = `${condition.field} < ${condition.value}`
            break
          default:
            conditionStr = `${condition.field} = '${condition.value}'`
        }
        
        groupConditions.push(conditionStr)
      }
    })
    
    if (groupConditions.length > 0) {
      const groupStr = groupConditions.join(' AND ')
      conditions.push(groupIndex > 0 ? `${group.operator} (${groupStr})` : `(${groupStr})`)
    }
  })
  
  return conditions.length > 0 
    ? `SELECT * FROM logs WHERE ${conditions.join(' ')}`
    : 'SELECT * FROM logs'
}

const isQueryValid = computed(() => {
  if (currentMode.value === 'sql') {
    return sqlQuery.value.trim().length > 0
  } else if (currentMode.value === 'natural') {
    return naturalQuery.value.trim().length > 0
  } else {
    return queryGroups.value.some(group => 
      group.conditions.some(condition => 
        condition.field && condition.value
      )
    )
  }
})

const estimatedResults = computed(() => {
  // 模拟估算结果数量
  return Math.floor(Math.random() * 1000) + 100
})

const executeQuery = () => {
  if (!isQueryValid.value) return
  
  // 添加到查询历史
  const newQuery = {
    id: Date.now(),
    name: `查询 ${format(new Date(), 'MM-dd HH:mm')}`,
    query: generatedQuery.value,
    timestamp: new Date(),
    resultCount: estimatedResults.value,
    executionTime: Math.floor(Math.random() * 100) + 20
  }
  
  queryHistory.value.unshift(newQuery)
  
  appStore.addNotification({
    type: 'success',
    message: `查询执行成功，找到 ${estimatedResults.value} 条结果`
  })
}

const saveQuery = () => {
  if (!isQueryValid.value) {
    appStore.addNotification({
      type: 'error',
      message: '请先构建有效的查询'
    })
    return
  }
  
  appStore.addNotification({
    type: 'success',
    message: '查询已保存'
  })
}

const loadQuery = (query) => {
  if (query.query.startsWith('SELECT')) {
    currentMode.value = 'sql'
    sqlQuery.value = query.query
  } else {
    currentMode.value = 'natural'
    naturalQuery.value = query.query
  }
  
  showQueryHistory.value = false
  
  appStore.addNotification({
    type: 'success',
    message: '查询已加载'
  })
}

const formatSQL = () => {
  // 简单的SQL格式化
  sqlQuery.value = sqlQuery.value
    .replace(/\s+/g, ' ')
    .replace(/SELECT/gi, 'SELECT')
    .replace(/FROM/gi, '\nFROM')
    .replace(/WHERE/gi, '\nWHERE')
    .replace(/AND/gi, '\n  AND')
    .replace(/OR/gi, '\n  OR')
    .trim()
}

const validateSQL = () => {
  // 简单的SQL验证
  const isValid = sqlQuery.value.toLowerCase().includes('select') && 
                  sqlQuery.value.toLowerCase().includes('from')
  
  appStore.addNotification({
    type: isValid ? 'success' : 'error',
    message: isValid ? 'SQL语法正确' : 'SQL语法错误'
  })
}

const processNaturalQuery = () => {
  // 模拟自然语言处理
  appStore.addNotification({
    type: 'info',
    message: '正在处理自然语言查询...'
  })
  
  setTimeout(() => {
    currentMode.value = 'sql'
    sqlQuery.value = `SELECT * FROM logs WHERE message LIKE '%${naturalQuery.value}%'`
    
    appStore.addNotification({
      type: 'success',
      message: '自然语言查询已转换为SQL'
    })
  }, 1000)
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}
</script>
