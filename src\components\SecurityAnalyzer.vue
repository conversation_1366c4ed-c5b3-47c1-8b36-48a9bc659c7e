<template>
  <div class="space-y-6">
    <!-- 安全概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">安全分析</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full"
              :class="getSecurityStatusColor(securityStatus.level)"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ getSecurityStatusText(securityStatus.level) }}
            </span>
          </div>
          <button
            @click="runSecurityScan"
            class="btn-primary"
            :disabled="isScanning"
          >
            <component :is="isScanning ? Loader : Shield" class="w-4 h-4 mr-2" :class="{ 'animate-spin': isScanning }" />
            {{ isScanning ? '扫描中...' : '安全扫描' }}
          </button>
        </div>
      </div>
      
      <!-- 安全指标 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <div class="text-2xl font-bold text-red-600 dark:text-red-400">{{ securityMetrics.threats }}</div>
          <div class="text-sm text-red-600 dark:text-red-400">威胁检测</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ securityMetrics.anomalies }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">异常行为</div>
        </div>
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ securityMetrics.blocked }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">已阻止攻击</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ securityMetrics.score }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">安全评分</div>
        </div>
      </div>
    </div>
    
    <!-- 威胁检测 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 实时威胁 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">实时威胁检测</h4>
        <div class="space-y-4">
          <div
            v-for="threat in realtimeThreats"
            :key="threat.id"
            class="border-l-4 rounded-r-lg p-4"
            :class="getThreatBorderClass(threat.severity)"
          >
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-3">
                <component :is="getThreatIcon(threat.type)" class="w-5 h-5 text-red-500" />
                <h5 class="font-medium text-gray-900 dark:text-white">{{ threat.title }}</h5>
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getSeverityClass(threat.severity)"
                >
                  {{ getSeverityLabel(threat.severity) }}
                </span>
              </div>
              <span class="text-xs text-gray-500">{{ formatTime(threat.timestamp) }}</span>
            </div>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ threat.description }}</p>
            <div class="text-xs text-gray-500">
              <span>来源IP: {{ threat.sourceIp }}</span>
              <span class="ml-4">目标: {{ threat.target }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 攻击统计 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">攻击类型统计</h4>
        <div class="h-64">
          <AttackTypesChart :data="attackTypesData" />
        </div>
      </div>
    </div>
    
    <!-- IP威胁情报 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">IP威胁情报</h4>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 恶意IP列表 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">恶意IP TOP 10</h5>
          <div class="space-y-3">
            <div
              v-for="ip in maliciousIPs"
              :key="ip.address"
              class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <div class="flex items-center space-x-3">
                <div
                  class="w-3 h-3 rounded-full"
                  :class="getThreatLevelColor(ip.threatLevel)"
                ></div>
                <div>
                  <div class="text-sm font-medium text-gray-900 dark:text-white">{{ ip.address }}</div>
                  <div class="text-xs text-gray-500">{{ ip.location }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-sm font-medium text-red-600">{{ ip.attempts }}</div>
                <div class="text-xs text-gray-500">攻击次数</div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 地理分布 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">攻击地理分布</h5>
          <div class="h-48">
            <GeoThreatChart :data="geoThreatData" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 安全规则管理 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">安全规则</h4>
        <button
          @click="showAddRule = true"
          class="btn-primary"
        >
          <component :is="Plus" class="w-4 h-4 mr-2" />
          添加规则
        </button>
      </div>
      
      <div class="space-y-4">
        <div
          v-for="rule in securityRules"
          :key="rule.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center space-x-3">
              <h5 class="font-medium text-gray-900 dark:text-white">{{ rule.name }}</h5>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="rule.enabled 
                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'"
              >
                {{ rule.enabled ? '启用' : '禁用' }}
              </span>
              <span
                class="px-2 py-1 text-xs font-medium rounded-full"
                :class="getRuleTypeClass(rule.type)"
              >
                {{ getRuleTypeLabel(rule.type) }}
              </span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="toggleRule(rule)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="rule.enabled ? Pause : Play" class="w-4 h-4" />
              </button>
              <button
                @click="editRule(rule)"
                class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="Edit" class="w-4 h-4" />
              </button>
              <button
                @click="deleteRule(rule)"
                class="p-2 text-red-400 hover:text-red-600 rounded"
              >
                <component :is="Trash2" class="w-4 h-4" />
              </button>
            </div>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">{{ rule.description }}</p>
          <div class="text-xs text-gray-500">
            <span>条件: {{ rule.condition }}</span>
            <span class="ml-4">动作: {{ rule.action }}</span>
            <span class="ml-4">触发: {{ rule.triggerCount }} 次</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 安全报告 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">安全报告</h4>
        <div class="flex items-center space-x-2">
          <select v-model="reportPeriod" class="input w-32">
            <option value="daily">每日</option>
            <option value="weekly">每周</option>
            <option value="monthly">每月</option>
          </select>
          <button
            @click="generateReport"
            class="btn-secondary"
          >
            <component :is="FileText" class="w-4 h-4 mr-2" />
            生成报告
          </button>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 威胁趋势 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">威胁趋势</h5>
          <div class="h-32">
            <ThreatTrendChart :data="threatTrendData" />
          </div>
        </div>
        
        <!-- 防护效果 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">防护效果</h5>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">阻止率</span>
              <span class="text-sm font-medium text-green-600">98.5%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">误报率</span>
              <span class="text-sm font-medium text-yellow-600">1.2%</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">响应时间</span>
              <span class="text-sm font-medium text-blue-600">0.3s</span>
            </div>
          </div>
        </div>
        
        <!-- 合规状态 -->
        <div>
          <h5 class="font-medium text-gray-900 dark:text-white mb-3">合规状态</h5>
          <div class="space-y-2">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">GDPR</span>
              <span class="text-sm font-medium text-green-600">合规</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">SOX</span>
              <span class="text-sm font-medium text-green-600">合规</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600 dark:text-gray-400">PCI DSS</span>
              <span class="text-sm font-medium text-yellow-600">部分合规</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  Shield, 
  Loader, 
  Plus, 
  Play, 
  Pause, 
  Edit, 
  Trash2, 
  FileText,
  AlertTriangle,
  Zap,
  Eye,
  Lock
} from 'lucide-vue-next'

// 导入图表组件
import AttackTypesChart from '@/components/charts/AttackTypesChart.vue'
import GeoThreatChart from '@/components/charts/GeoThreatChart.vue'
import ThreatTrendChart from '@/components/charts/ThreatTrendChart.vue'

const appStore = useAppStore()

const isScanning = ref(false)
const showAddRule = ref(false)
const reportPeriod = ref('daily')

const securityStatus = reactive({
  level: 'warning' // safe, warning, critical
})

const securityMetrics = reactive({
  threats: 23,
  anomalies: 8,
  blocked: 156,
  score: 85
})

const realtimeThreats = ref([
  {
    id: 1,
    type: 'brute_force',
    title: 'SSH暴力破解攻击',
    description: '检测到来自*************的SSH暴力破解尝试',
    severity: 'high',
    sourceIp: '*************',
    target: 'server01:22',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: 2,
    type: 'sql_injection',
    title: 'SQL注入攻击',
    description: 'Web应用检测到SQL注入攻击尝试',
    severity: 'critical',
    sourceIp: '************',
    target: '/api/users',
    timestamp: new Date(Date.now() - 600000)
  },
  {
    id: 3,
    type: 'ddos',
    title: 'DDoS攻击',
    description: '检测到大量异常请求，疑似DDoS攻击',
    severity: 'medium',
    sourceIp: '*************',
    target: 'web服务器',
    timestamp: new Date(Date.now() - 900000)
  }
])

const maliciousIPs = ref([
  { address: '*************', location: '中国 北京', attempts: 234, threatLevel: 'high' },
  { address: '************', location: '美国 加州', attempts: 189, threatLevel: 'critical' },
  { address: '*************', location: '俄罗斯 莫斯科', attempts: 156, threatLevel: 'medium' },
  { address: '*********', location: '德国 柏林', attempts: 123, threatLevel: 'high' },
  { address: '***********', location: '英国 伦敦', attempts: 98, threatLevel: 'medium' }
])

const securityRules = ref([
  {
    id: 1,
    name: '暴力破解检测',
    description: '检测短时间内多次登录失败的行为',
    type: 'detection',
    condition: 'failed_login_count > 5 in 5 minutes',
    action: 'block_ip',
    enabled: true,
    triggerCount: 45
  },
  {
    id: 2,
    name: 'SQL注入防护',
    description: '检测和阻止SQL注入攻击',
    type: 'prevention',
    condition: 'request contains sql_injection_pattern',
    action: 'block_request',
    enabled: true,
    triggerCount: 23
  },
  {
    id: 3,
    name: '异常流量监控',
    description: '监控异常的网络流量模式',
    type: 'monitoring',
    condition: 'request_rate > 1000/minute',
    action: 'alert',
    enabled: false,
    triggerCount: 12
  }
])

const attackTypesData = ref([])
const geoThreatData = ref([])
const threatTrendData = ref([])

const getSecurityStatusColor = (level) => {
  const colors = {
    safe: 'bg-green-500',
    warning: 'bg-yellow-500',
    critical: 'bg-red-500'
  }
  return colors[level] || colors.warning
}

const getSecurityStatusText = (level) => {
  const texts = {
    safe: '安全',
    warning: '警告',
    critical: '严重'
  }
  return texts[level] || '未知'
}

const getThreatIcon = (type) => {
  const icons = {
    brute_force: Lock,
    sql_injection: Zap,
    ddos: AlertTriangle,
    malware: Shield,
    phishing: Eye
  }
  return icons[type] || AlertTriangle
}

const getThreatBorderClass = (severity) => {
  const classes = {
    low: 'border-green-500 bg-green-50 dark:bg-green-900/20',
    medium: 'border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20',
    high: 'border-orange-500 bg-orange-50 dark:bg-orange-900/20',
    critical: 'border-red-500 bg-red-50 dark:bg-red-900/20'
  }
  return classes[severity] || classes.medium
}

const getSeverityClass = (severity) => {
  const classes = {
    low: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    medium: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    high: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    critical: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
  }
  return classes[severity] || classes.medium
}

const getSeverityLabel = (severity) => {
  const labels = {
    low: '低',
    medium: '中',
    high: '高',
    critical: '严重'
  }
  return labels[severity] || '中'
}

const getThreatLevelColor = (level) => {
  const colors = {
    low: 'bg-green-500',
    medium: 'bg-yellow-500',
    high: 'bg-orange-500',
    critical: 'bg-red-500'
  }
  return colors[level] || colors.medium
}

const getRuleTypeClass = (type) => {
  const classes = {
    detection: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    prevention: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    monitoring: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
  }
  return classes[type] || classes.detection
}

const getRuleTypeLabel = (type) => {
  const labels = {
    detection: '检测',
    prevention: '防护',
    monitoring: '监控'
  }
  return labels[type] || '检测'
}

const runSecurityScan = async () => {
  isScanning.value = true
  
  try {
    // 模拟安全扫描
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 更新安全指标
    securityMetrics.threats = Math.floor(Math.random() * 50) + 10
    securityMetrics.anomalies = Math.floor(Math.random() * 20) + 5
    securityMetrics.blocked = Math.floor(Math.random() * 200) + 100
    securityMetrics.score = Math.floor(Math.random() * 30) + 70
    
    appStore.addNotification({
      type: 'success',
      message: '安全扫描完成，发现了多个安全威胁'
    })
  } catch (error) {
    appStore.addNotification({
      type: 'error',
      message: '安全扫描失败，请重试'
    })
  } finally {
    isScanning.value = false
  }
}

const toggleRule = (rule) => {
  rule.enabled = !rule.enabled
  appStore.addNotification({
    type: 'success',
    message: `安全规则 "${rule.name}" 已${rule.enabled ? '启用' : '禁用'}`
  })
}

const editRule = (rule) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑安全规则: ${rule.name}`
  })
}

const deleteRule = (rule) => {
  if (confirm(`确定要删除安全规则 "${rule.name}" 吗？`)) {
    const index = securityRules.value.findIndex(r => r.id === rule.id)
    if (index > -1) {
      securityRules.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '安全规则已删除'
      })
    }
  }
}

const generateReport = () => {
  appStore.addNotification({
    type: 'success',
    message: `${reportPeriod.value === 'daily' ? '每日' : reportPeriod.value === 'weekly' ? '每周' : '每月'}安全报告生成中...`
  })
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}

// 初始化图表数据
attackTypesData.value = [
  { name: 'SQL注入', value: 45 },
  { name: '暴力破解', value: 32 },
  { name: 'DDoS', value: 28 },
  { name: 'XSS', value: 15 },
  { name: '其他', value: 12 }
]

geoThreatData.value = [
  { country: '中国', threats: 234 },
  { country: '美国', threats: 189 },
  { country: '俄罗斯', threats: 156 },
  { country: '德国', threats: 123 },
  { country: '英国', threats: 98 }
]

threatTrendData.value = Array.from({ length: 24 }, (_, i) => ({
  time: new Date(Date.now() - (23 - i) * 3600000).toISOString(),
  value: Math.floor(Math.random() * 50) + 10
}))
</script>
