<template>
  <div 
    v-if="show"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="$emit('close')"
  >
    <div 
      class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary-100 dark:bg-primary-900/20 rounded-lg">
            <component :is="Keyboard" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">快捷键帮助</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">掌握这些快捷键，让你的日志查看更高效</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <component :is="X" class="w-5 h-5" />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <!-- 基础操作 -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <component :is="Zap" class="w-5 h-5 mr-2 text-yellow-500" />
              基础操作
            </h3>
            <div class="space-y-3">
              <div
                v-for="shortcut in basicShortcuts"
                :key="shortcut.key"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white">{{ shortcut.action }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ shortcut.description }}</div>
                </div>
                <div class="flex items-center space-x-1">
                  <kbd
                    v-for="key in shortcut.keys"
                    :key="key"
                    class="px-2 py-1 text-xs font-mono bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-sm"
                  >
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 导航操作 -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <component :is="Navigation" class="w-5 h-5 mr-2 text-blue-500" />
              导航操作
            </h3>
            <div class="space-y-3">
              <div
                v-for="shortcut in navigationShortcuts"
                :key="shortcut.key"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white">{{ shortcut.action }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ shortcut.description }}</div>
                </div>
                <div class="flex items-center space-x-1">
                  <kbd
                    v-for="key in shortcut.keys"
                    :key="key"
                    class="px-2 py-1 text-xs font-mono bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-sm"
                  >
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 搜索和过滤 -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <component :is="Search" class="w-5 h-5 mr-2 text-green-500" />
              搜索和过滤
            </h3>
            <div class="space-y-3">
              <div
                v-for="shortcut in searchShortcuts"
                :key="shortcut.key"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white">{{ shortcut.action }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ shortcut.description }}</div>
                </div>
                <div class="flex items-center space-x-1">
                  <kbd
                    v-for="key in shortcut.keys"
                    :key="key"
                    class="px-2 py-1 text-xs font-mono bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-sm"
                  >
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 视图操作 -->
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <component :is="Eye" class="w-5 h-5 mr-2 text-purple-500" />
              视图操作
            </h3>
            <div class="space-y-3">
              <div
                v-for="shortcut in viewShortcuts"
                :key="shortcut.key"
                class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
              >
                <div class="flex-1">
                  <div class="font-medium text-gray-900 dark:text-white">{{ shortcut.action }}</div>
                  <div class="text-sm text-gray-600 dark:text-gray-400">{{ shortcut.description }}</div>
                </div>
                <div class="flex items-center space-x-1">
                  <kbd
                    v-for="key in shortcut.keys"
                    :key="key"
                    class="px-2 py-1 text-xs font-mono bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded shadow-sm"
                  >
                    {{ key }}
                  </kbd>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 高级技巧 -->
        <div class="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
          <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-200 flex items-center mb-4">
            <component :is="Lightbulb" class="w-5 h-5 mr-2" />
            高级技巧
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div class="space-y-2">
              <div class="font-medium text-blue-800 dark:text-blue-300">正则表达式搜索</div>
              <div class="text-blue-700 dark:text-blue-400">在搜索框中使用正则表达式进行高级搜索，如 <code class="bg-white dark:bg-gray-800 px-1 rounded">error.*timeout</code></div>
            </div>
            <div class="space-y-2">
              <div class="font-medium text-blue-800 dark:text-blue-300">快速过滤</div>
              <div class="text-blue-700 dark:text-blue-400">使用快速过滤按钮可以快速应用常用的过滤条件</div>
            </div>
            <div class="space-y-2">
              <div class="font-medium text-blue-800 dark:text-blue-300">多选操作</div>
              <div class="text-blue-700 dark:text-blue-400">按住 <kbd class="bg-white dark:bg-gray-800 px-1 rounded">Ctrl</kbd> 键可以选择多行日志进行批量操作</div>
            </div>
            <div class="space-y-2">
              <div class="font-medium text-blue-800 dark:text-blue-300">书签管理</div>
              <div class="text-blue-700 dark:text-blue-400">为重要的日志行添加书签，方便后续快速定位</div>
            </div>
          </div>
        </div>
        
        <!-- 自定义快捷键 -->
        <div class="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h4 class="font-medium text-gray-900 dark:text-white">自定义快捷键</h4>
            <button
              @click="showCustomShortcuts = !showCustomShortcuts"
              class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
            >
              {{ showCustomShortcuts ? '隐藏' : '显示' }}
            </button>
          </div>
          <div v-if="showCustomShortcuts" class="text-sm text-gray-600 dark:text-gray-400">
            <p>你可以在设置中自定义快捷键，让操作更符合你的使用习惯。</p>
            <button class="mt-2 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
              前往设置 →
            </button>
          </div>
        </div>
      </div>
      
      <!-- 底部 -->
      <div class="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div class="text-sm text-gray-600 dark:text-gray-400">
          按 <kbd class="px-2 py-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded text-xs font-mono">?</kbd> 随时打开此帮助
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="printShortcuts"
            class="px-4 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-white dark:hover:bg-gray-700 transition-colors"
          >
            <component :is="Printer" class="w-4 h-4 mr-2 inline" />
            打印
          </button>
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            知道了
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  Keyboard, 
  X, 
  Zap, 
  Navigation, 
  Search, 
  Eye, 
  Lightbulb, 
  Printer 
} from 'lucide-vue-next'

defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

defineEmits(['close'])

const showCustomShortcuts = ref(false)

const basicShortcuts = [
  {
    key: 'connect',
    action: '连接/断开',
    description: '切换日志流连接状态',
    keys: ['Ctrl', 'Enter']
  },
  {
    key: 'clear',
    action: '清空终端',
    description: '清除所有日志内容',
    keys: ['Ctrl', 'L']
  },
  {
    key: 'copy',
    action: '复制选中行',
    description: '复制当前选中的日志行',
    keys: ['Ctrl', 'C']
  },
  {
    key: 'select-all',
    action: '全选',
    description: '选择所有可见的日志行',
    keys: ['Ctrl', 'A']
  },
  {
    key: 'refresh',
    action: '刷新',
    description: '刷新日志数据',
    keys: ['F5']
  }
]

const navigationShortcuts = [
  {
    key: 'scroll-top',
    action: '跳转到顶部',
    description: '滚动到日志的最开始',
    keys: ['Home']
  },
  {
    key: 'scroll-bottom',
    action: '跳转到底部',
    description: '滚动到日志的最末尾',
    keys: ['End']
  },
  {
    key: 'page-up',
    action: '向上翻页',
    description: '向上滚动一页',
    keys: ['Page Up']
  },
  {
    key: 'page-down',
    action: '向下翻页',
    description: '向下滚动一页',
    keys: ['Page Down']
  },
  {
    key: 'line-up',
    action: '上一行',
    description: '选择上一行日志',
    keys: ['↑']
  },
  {
    key: 'line-down',
    action: '下一行',
    description: '选择下一行日志',
    keys: ['↓']
  }
]

const searchShortcuts = [
  {
    key: 'search',
    action: '打开搜索',
    description: '聚焦到搜索输入框',
    keys: ['Ctrl', 'F']
  },
  {
    key: 'search-next',
    action: '下一个结果',
    description: '跳转到下一个搜索结果',
    keys: ['F3']
  },
  {
    key: 'search-prev',
    action: '上一个结果',
    description: '跳转到上一个搜索结果',
    keys: ['Shift', 'F3']
  },
  {
    key: 'clear-search',
    action: '清空搜索',
    description: '清除搜索条件',
    keys: ['Esc']
  },
  {
    key: 'filter-error',
    action: '错误过滤',
    description: '快速过滤错误日志',
    keys: ['Ctrl', '1']
  },
  {
    key: 'filter-warn',
    action: '警告过滤',
    description: '快速过滤警告日志',
    keys: ['Ctrl', '2']
  }
]

const viewShortcuts = [
  {
    key: 'fullscreen',
    action: '全屏模式',
    description: '切换全屏显示',
    keys: ['F11']
  },
  {
    key: 'word-wrap',
    action: '自动换行',
    description: '切换文本自动换行',
    keys: ['Ctrl', 'W']
  },
  {
    key: 'line-numbers',
    action: '行号显示',
    description: '切换行号显示',
    keys: ['Ctrl', 'Shift', 'L']
  },
  {
    key: 'timestamps',
    action: '时间戳显示',
    description: '切换时间戳显示',
    keys: ['Ctrl', 'T']
  },
  {
    key: 'auto-scroll',
    action: '自动滚动',
    description: '切换自动滚动到底部',
    keys: ['Ctrl', 'D']
  },
  {
    key: 'theme-switch',
    action: '切换主题',
    description: '在不同终端主题间切换',
    keys: ['Ctrl', 'Shift', 'T']
  }
]

const printShortcuts = () => {
  window.print()
}
</script>

<style scoped>
kbd {
  font-family: inherit;
  font-size: 0.75rem;
}

code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

@media print {
  .fixed {
    position: static !important;
  }
  
  .bg-black {
    background: white !important;
  }
  
  .bg-opacity-50 {
    background-opacity: 1 !important;
  }
}
</style>
