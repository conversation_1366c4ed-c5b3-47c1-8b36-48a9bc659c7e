<template>
  <div 
    class="fixed left-0 top-0 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-all duration-300 z-30"
    :class="appStore.sidebarCollapsed ? 'w-16' : 'w-64'"
  >
    <!-- Logo -->
    <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
      <div v-if="!appStore.sidebarCollapsed" class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
          <component :is="Activity" class="w-5 h-5 text-white" />
        </div>
        <span class="text-lg font-semibold text-gray-900 dark:text-white">日志管理</span>
      </div>
      <button
        @click="appStore.toggleSidebar"
        class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      >
        <component :is="Menu" class="w-5 h-5 text-gray-600 dark:text-gray-400" />
      </button>
    </div>
    
    <!-- Navigation -->
    <nav class="p-4 space-y-2">
      <router-link
        v-for="route in routes"
        :key="route.name"
        :to="route.path"
        class="flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors group"
        :class="[
          $route.name === route.name
            ? 'bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300'
            : 'text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700'
        ]"
      >
        <component 
          :is="getIcon(route.meta.icon)" 
          class="w-5 h-5 flex-shrink-0"
          :class="$route.name === route.name ? 'text-primary-600 dark:text-primary-400' : ''"
        />
        <span 
          v-if="!appStore.sidebarCollapsed" 
          class="font-medium"
        >
          {{ route.meta.title }}
        </span>
      </router-link>
    </nav>
    
    <!-- Status Indicators -->
    <div v-if="!appStore.sidebarCollapsed" class="absolute bottom-4 left-4 right-4">
      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 space-y-2">
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">实时模式</span>
          <div class="flex items-center space-x-1">
            <div
              class="w-2 h-2 rounded-full"
              :class="logStore.isRealTimeMode ? 'bg-green-500 animate-pulse' : 'bg-gray-400'"
            ></div>
            <span class="text-xs text-gray-500">
              {{ logStore.isRealTimeMode ? '开启' : '关闭' }}
            </span>
          </div>
        </div>
        <div class="flex items-center justify-between text-sm">
          <span class="text-gray-600 dark:text-gray-400">日志总数</span>
          <span class="font-medium text-gray-900 dark:text-white">
            {{ logStore.logs.length.toLocaleString() }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores/appStore'
import { useLogStore } from '@/stores/logStore'
import {
  Activity,
  Menu,
  BarChart3,
  FileText,
  TrendingUp,
  Settings,
  Terminal
} from 'lucide-vue-next'

const route = useRoute()
const appStore = useAppStore()
const logStore = useLogStore()

const routes = computed(() => [
  {
    path: '/',
    name: 'Dashboard',
    meta: { title: '仪表板', icon: 'BarChart3' }
  },
  {
    path: '/logs',
    name: 'LogViewer',
    meta: { title: '日志查看器', icon: 'FileText' }
  },
  {
    path: '/live-logs',
    name: 'LiveLogs',
    meta: { title: '实时终端', icon: 'Terminal' }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    meta: { title: '数据分析', icon: 'TrendingUp' }
  },
  {
    path: '/settings',
    name: 'Settings',
    meta: { title: '系统设置', icon: 'Settings' }
  }
])

const getIcon = (iconName) => {
  const icons = {
    BarChart3,
    FileText,
    Terminal,
    TrendingUp,
    Settings
  }
  return icons[iconName] || FileText
}
</script>
