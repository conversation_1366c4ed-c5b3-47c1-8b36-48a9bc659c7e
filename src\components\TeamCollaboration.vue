<template>
  <div class="space-y-6">
    <!-- 团队概览 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">团队协作</h3>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <div class="flex -space-x-2">
              <div
                v-for="user in onlineUsers"
                :key="user.id"
                class="w-8 h-8 rounded-full border-2 border-white dark:border-gray-800 flex items-center justify-center text-xs font-medium text-white"
                :style="{ backgroundColor: user.color }"
                :title="user.name"
              >
                {{ user.name.charAt(0) }}
              </div>
            </div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ onlineUsers.length }} 人在线
            </span>
          </div>
          <button
            @click="showInviteModal = true"
            class="btn-primary"
          >
            <component :is="UserPlus" class="w-4 h-4 mr-2" />
            邀请成员
          </button>
        </div>
      </div>
      
      <!-- 活动统计 -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ teamStats.activeUsers }}</div>
          <div class="text-sm text-blue-600 dark:text-blue-400">活跃用户</div>
        </div>
        <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ teamStats.sharedDashboards }}</div>
          <div class="text-sm text-green-600 dark:text-green-400">共享仪表板</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{{ teamStats.annotations }}</div>
          <div class="text-sm text-yellow-600 dark:text-yellow-400">标注</div>
        </div>
        <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ teamStats.alerts }}</div>
          <div class="text-sm text-purple-600 dark:text-purple-400">团队告警</div>
        </div>
      </div>
    </div>
    
    <!-- 实时协作 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 共享视图 -->
      <div class="card p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-md font-semibold text-gray-900 dark:text-white">共享视图</h4>
          <button
            @click="createSharedView"
            class="btn-secondary text-sm"
          >
            <component :is="Share2" class="w-4 h-4 mr-2" />
            创建共享
          </button>
        </div>
        
        <div class="space-y-3">
          <div
            v-for="view in sharedViews"
            :key="view.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex items-center space-x-3">
              <component :is="getViewIcon(view.type)" class="w-5 h-5 text-gray-500" />
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ view.name }}</div>
                <div class="text-xs text-gray-500">{{ view.creator }} • {{ view.viewers }} 人查看</div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="joinView(view)"
                class="text-sm text-primary-600 hover:text-primary-700"
              >
                加入
              </button>
              <button
                @click="copyViewLink(view)"
                class="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded"
              >
                <component :is="Copy" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 团队聊天 -->
      <div class="card p-6">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">团队聊天</h4>
        
        <div class="h-64 overflow-y-auto mb-4 space-y-3">
          <div
            v-for="message in chatMessages"
            :key="message.id"
            class="flex items-start space-x-3"
          >
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium text-white flex-shrink-0"
              :style="{ backgroundColor: message.user.color }"
            >
              {{ message.user.name.charAt(0) }}
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center space-x-2">
                <span class="text-sm font-medium text-gray-900 dark:text-white">{{ message.user.name }}</span>
                <span class="text-xs text-gray-500">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">{{ message.content }}</div>
              <div v-if="message.logRef" class="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                <div class="font-medium">关联日志:</div>
                <div class="text-gray-600 dark:text-gray-400">{{ message.logRef }}</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <input
            v-model="newMessage"
            @keyup.enter="sendMessage"
            type="text"
            placeholder="输入消息..."
            class="input flex-1"
          />
          <button
            @click="sendMessage"
            class="btn-primary"
            :disabled="!newMessage.trim()"
          >
            <component :is="Send" class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
    
    <!-- 日志标注 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-md font-semibold text-gray-900 dark:text-white">日志标注</h4>
        <button
          @click="showAnnotationModal = true"
          class="btn-primary"
        >
          <component :is="MessageSquare" class="w-4 h-4 mr-2" />
          添加标注
        </button>
      </div>
      
      <div class="space-y-4">
        <div
          v-for="annotation in annotations"
          :key="annotation.id"
          class="border border-gray-200 dark:border-gray-700 rounded-lg p-4"
        >
          <div class="flex items-start justify-between mb-2">
            <div class="flex items-center space-x-3">
              <div
                class="w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium text-white"
                :style="{ backgroundColor: annotation.user.color }"
              >
                {{ annotation.user.name.charAt(0) }}
              </div>
              <div>
                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ annotation.user.name }}</div>
                <div class="text-xs text-gray-500">{{ formatTime(annotation.timestamp) }}</div>
              </div>
            </div>
            <span
              class="px-2 py-1 text-xs font-medium rounded-full"
              :class="getAnnotationTypeClass(annotation.type)"
            >
              {{ getAnnotationTypeLabel(annotation.type) }}
            </span>
          </div>
          
          <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ annotation.content }}</div>
          
          <div class="bg-gray-50 dark:bg-gray-800 rounded p-3">
            <div class="text-xs text-gray-500 mb-1">关联日志:</div>
            <div class="text-sm font-mono text-gray-700 dark:text-gray-300">{{ annotation.logEntry }}</div>
          </div>
          
          <div class="flex items-center justify-between mt-3">
            <div class="flex items-center space-x-4 text-xs text-gray-500">
              <span>{{ annotation.likes }} 点赞</span>
              <span>{{ annotation.replies }} 回复</span>
            </div>
            <div class="flex items-center space-x-2">
              <button
                @click="likeAnnotation(annotation)"
                class="text-xs text-gray-500 hover:text-primary-600"
              >
                <component :is="ThumbsUp" class="w-4 h-4" />
              </button>
              <button
                @click="replyAnnotation(annotation)"
                class="text-xs text-gray-500 hover:text-primary-600"
              >
                <component :is="MessageCircle" class="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 团队权限管理 -->
    <div class="card p-6">
      <h4 class="text-md font-semibold text-gray-900 dark:text-white mb-4">权限管理</h4>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                成员
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                权限
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                最后活动
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="member in teamMembers" :key="member.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div
                    class="w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium text-white mr-3"
                    :style="{ backgroundColor: member.color }"
                  >
                    {{ member.name.charAt(0) }}
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ member.name }}</div>
                    <div class="text-sm text-gray-500">{{ member.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getRoleClass(member.role)"
                >
                  {{ getRoleLabel(member.role) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ member.permissions.join(', ') }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatTime(member.lastActivity) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="editMember(member)"
                  class="text-primary-600 hover:text-primary-900 mr-3"
                >
                  编辑
                </button>
                <button
                  @click="removeMember(member)"
                  class="text-red-600 hover:text-red-900"
                >
                  移除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { format } from 'date-fns'
import { 
  UserPlus, 
  Share2, 
  Copy, 
  Send, 
  MessageSquare, 
  ThumbsUp, 
  MessageCircle,
  Eye,
  BarChart3,
  Filter
} from 'lucide-vue-next'

const appStore = useAppStore()

const showInviteModal = ref(false)
const showAnnotationModal = ref(false)
const newMessage = ref('')

const teamStats = reactive({
  activeUsers: 8,
  sharedDashboards: 12,
  annotations: 45,
  alerts: 23
})

const onlineUsers = ref([
  { id: 1, name: '张三', color: '#3b82f6' },
  { id: 2, name: '李四', color: '#ef4444' },
  { id: 3, name: '王五', color: '#10b981' },
  { id: 4, name: '赵六', color: '#f59e0b' }
])

const sharedViews = ref([
  {
    id: 1,
    name: '生产环境错误监控',
    type: 'dashboard',
    creator: '张三',
    viewers: 5,
    url: '/shared/prod-errors'
  },
  {
    id: 2,
    name: 'API性能分析',
    type: 'analytics',
    creator: '李四',
    viewers: 3,
    url: '/shared/api-performance'
  },
  {
    id: 3,
    name: '安全事件过滤器',
    type: 'filter',
    creator: '王五',
    viewers: 7,
    url: '/shared/security-filter'
  }
])

const chatMessages = ref([
  {
    id: 1,
    user: { name: '张三', color: '#3b82f6' },
    content: '发现生产环境有异常错误增加',
    timestamp: new Date(Date.now() - 300000),
    logRef: '[ERROR] Database connection timeout at 2024-01-15 10:30:45'
  },
  {
    id: 2,
    user: { name: '李四', color: '#ef4444' },
    content: '我来检查一下数据库连接池配置',
    timestamp: new Date(Date.now() - 240000)
  },
  {
    id: 3,
    user: { name: '王五', color: '#10b981' },
    content: '已经调整了连接池大小，请继续监控',
    timestamp: new Date(Date.now() - 120000)
  }
])

const annotations = ref([
  {
    id: 1,
    user: { name: '张三', color: '#3b82f6' },
    type: 'incident',
    content: '这个错误可能与数据库连接池配置有关，需要调整最大连接数',
    logEntry: '[ERROR] 2024-01-15 10:30:45 - Connection pool exhausted',
    timestamp: new Date(Date.now() - 1800000),
    likes: 3,
    replies: 2
  },
  {
    id: 2,
    user: { name: '李四', color: '#ef4444' },
    type: 'solution',
    content: '已解决：增加了连接池大小并优化了连接超时设置',
    logEntry: '[INFO] 2024-01-15 11:15:30 - Connection pool reconfigured',
    timestamp: new Date(Date.now() - 900000),
    likes: 5,
    replies: 1
  }
])

const teamMembers = ref([
  {
    id: 1,
    name: '张三',
    email: '<EMAIL>',
    role: 'admin',
    color: '#3b82f6',
    permissions: ['查看', '编辑', '管理'],
    lastActivity: new Date(Date.now() - 300000)
  },
  {
    id: 2,
    name: '李四',
    email: '<EMAIL>',
    role: 'analyst',
    color: '#ef4444',
    permissions: ['查看', '编辑'],
    lastActivity: new Date(Date.now() - 600000)
  },
  {
    id: 3,
    name: '王五',
    email: '<EMAIL>',
    role: 'viewer',
    color: '#10b981',
    permissions: ['查看'],
    lastActivity: new Date(Date.now() - 1800000)
  }
])

const getViewIcon = (type) => {
  const icons = {
    dashboard: BarChart3,
    analytics: Eye,
    filter: Filter
  }
  return icons[type] || Eye
}

const createSharedView = () => {
  appStore.addNotification({
    type: 'success',
    message: '共享视图创建成功'
  })
}

const joinView = (view) => {
  appStore.addNotification({
    type: 'info',
    message: `已加入共享视图: ${view.name}`
  })
}

const copyViewLink = (view) => {
  navigator.clipboard.writeText(`${window.location.origin}${view.url}`)
  appStore.addNotification({
    type: 'success',
    message: '链接已复制到剪贴板'
  })
}

const sendMessage = () => {
  if (!newMessage.value.trim()) return
  
  const message = {
    id: Date.now(),
    user: { name: '当前用户', color: '#8b5cf6' },
    content: newMessage.value,
    timestamp: new Date()
  }
  
  chatMessages.value.push(message)
  newMessage.value = ''
}

const likeAnnotation = (annotation) => {
  annotation.likes++
  appStore.addNotification({
    type: 'success',
    message: '已点赞'
  })
}

const replyAnnotation = (annotation) => {
  appStore.addNotification({
    type: 'info',
    message: '回复功能开发中'
  })
}

const getAnnotationTypeClass = (type) => {
  const classes = {
    incident: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    solution: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    note: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
  }
  return classes[type] || classes.note
}

const getAnnotationTypeLabel = (type) => {
  const labels = {
    incident: '事件',
    solution: '解决方案',
    note: '备注'
  }
  return labels[type] || '备注'
}

const getRoleClass = (role) => {
  const classes = {
    admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    analyst: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    viewer: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
  }
  return classes[role] || classes.viewer
}

const getRoleLabel = (role) => {
  const labels = {
    admin: '管理员',
    analyst: '分析师',
    viewer: '查看者'
  }
  return labels[role] || '查看者'
}

const editMember = (member) => {
  appStore.addNotification({
    type: 'info',
    message: `编辑成员: ${member.name}`
  })
}

const removeMember = (member) => {
  if (confirm(`确定要移除成员 "${member.name}" 吗？`)) {
    const index = teamMembers.value.findIndex(m => m.id === member.id)
    if (index > -1) {
      teamMembers.value.splice(index, 1)
      appStore.addNotification({
        type: 'success',
        message: '成员已移除'
      })
    }
  }
}

const formatTime = (timestamp) => {
  return format(timestamp, 'MM-dd HH:mm')
}
</script>
