<template>
  <div 
    v-if="show"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="$emit('close')"
  >
    <div 
      class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
      @click.stop
    >
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <div class="p-2 bg-primary-100 dark:bg-primary-900/20 rounded-lg">
            <component :is="Palette" class="w-6 h-6 text-primary-600 dark:text-primary-400" />
          </div>
          <div>
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">终端主题管理</h2>
            <p class="text-sm text-gray-600 dark:text-gray-400">选择或自定义你的终端主题</p>
          </div>
        </div>
        <button
          @click="$emit('close')"
          class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        >
          <component :is="X" class="w-5 h-5" />
        </button>
      </div>
      
      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        <!-- 预设主题 -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">预设主题</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="theme in presetThemes"
              :key="theme.name"
              class="theme-card border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-lg"
              :class="currentTheme.name === theme.name 
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'"
              @click="selectTheme(theme)"
            >
              <!-- 主题预览 -->
              <div 
                class="theme-preview rounded-lg p-3 mb-3 font-mono text-xs"
                :style="{ 
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text
                }"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-2 h-2 rounded-full bg-red-500"></div>
                  <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                  <div class="w-2 h-2 rounded-full bg-green-500"></div>
                  <span class="ml-2 text-xs opacity-75">{{ theme.label }}</span>
                </div>
                <div class="space-y-1">
                  <div class="flex items-center space-x-2">
                    <span class="opacity-60">12:34:56</span>
                    <span 
                      class="px-1 rounded text-xs"
                      :style="{ backgroundColor: theme.colors.error, color: theme.colors.background }"
                    >
                      ERROR
                    </span>
                    <span class="opacity-80">[nginx]</span>
                  </div>
                  <div :style="{ color: theme.colors.text }">
                    Connection timeout error
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="opacity-60">12:34:57</span>
                    <span 
                      class="px-1 rounded text-xs"
                      :style="{ backgroundColor: theme.colors.info, color: theme.colors.background }"
                    >
                      INFO
                    </span>
                    <span class="opacity-80">[api]</span>
                  </div>
                  <div :style="{ color: theme.colors.text }">
                    Request processed successfully
                  </div>
                </div>
              </div>
              
              <!-- 主题信息 -->
              <div class="text-center">
                <h4 class="font-semibold text-gray-900 dark:text-white">{{ theme.label }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ theme.description }}</p>
                <div class="flex justify-center space-x-1 mt-2">
                  <div 
                    v-for="color in Object.values(theme.colors)"
                    :key="color"
                    class="w-4 h-4 rounded-full border border-gray-300"
                    :style="{ backgroundColor: color }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 自定义主题 -->
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">自定义主题</h3>
            <button
              @click="showCustomEditor = !showCustomEditor"
              class="px-4 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              {{ showCustomEditor ? '隐藏编辑器' : '创建自定义主题' }}
            </button>
          </div>
          
          <div v-if="showCustomEditor" class="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 颜色配置 -->
              <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-white">颜色配置</h4>
                
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      背景色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.background"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.background"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      文本色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.text"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.text"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      错误色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.error"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.error"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      警告色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.warning"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.warning"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      信息色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.info"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.info"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      边框色
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.border"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.border"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      头部背景
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.headerBg"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.headerBg"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      内容背景
                    </label>
                    <div class="flex items-center space-x-2">
                      <input
                        v-model="customTheme.colors.contentBg"
                        type="color"
                        class="w-12 h-8 rounded border border-gray-300 cursor-pointer"
                      />
                      <input
                        v-model="customTheme.colors.contentBg"
                        type="text"
                        class="flex-1 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      />
                    </div>
                  </div>
                </div>
                
                <div class="pt-4">
                  <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    主题名称
                  </label>
                  <input
                    v-model="customTheme.label"
                    type="text"
                    placeholder="输入主题名称..."
                    class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              <!-- 实时预览 -->
              <div class="space-y-4">
                <h4 class="font-medium text-gray-900 dark:text-white">实时预览</h4>
                
                <div 
                  class="theme-preview rounded-lg p-4 font-mono text-sm border"
                  :style="{ 
                    backgroundColor: customTheme.colors.background,
                    color: customTheme.colors.text,
                    borderColor: customTheme.colors.text + '40'
                  }"
                >
                  <div class="flex items-center space-x-2 mb-3">
                    <div class="w-3 h-3 rounded-full bg-red-500"></div>
                    <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <div class="w-3 h-3 rounded-full bg-green-500"></div>
                    <span class="ml-2 text-sm opacity-75">{{ customTheme.label || '自定义主题' }}</span>
                  </div>
                  
                  <div class="space-y-2">
                    <div class="flex items-center space-x-3">
                      <span class="opacity-60 text-xs">12:34:56.123</span>
                      <span 
                        class="px-2 py-1 rounded text-xs font-bold"
                        :style="{ backgroundColor: customTheme.colors.error, color: customTheme.colors.background }"
                      >
                        ERROR
                      </span>
                      <span class="opacity-80 text-xs">[nginx]</span>
                    </div>
                    <div class="ml-20">Connection timeout error occurred</div>
                    
                    <div class="flex items-center space-x-3">
                      <span class="opacity-60 text-xs">12:34:57.456</span>
                      <span 
                        class="px-2 py-1 rounded text-xs font-bold"
                        :style="{ backgroundColor: customTheme.colors.warning, color: customTheme.colors.background }"
                      >
                        WARN
                      </span>
                      <span class="opacity-80 text-xs">[mysql]</span>
                    </div>
                    <div class="ml-20">Slow query detected: 1.2s</div>
                    
                    <div class="flex items-center space-x-3">
                      <span class="opacity-60 text-xs">12:34:58.789</span>
                      <span 
                        class="px-2 py-1 rounded text-xs font-bold"
                        :style="{ backgroundColor: customTheme.colors.info, color: customTheme.colors.background }"
                      >
                        INFO
                      </span>
                      <span class="opacity-80 text-xs">[api]</span>
                    </div>
                    <div class="ml-20">Request processed successfully</div>
                  </div>
                </div>
                
                <div class="flex space-x-2">
                  <button
                    @click="saveCustomTheme"
                    class="flex-1 px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    保存主题
                  </button>
                  <button
                    @click="applyCustomTheme"
                    class="flex-1 px-4 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    应用主题
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 已保存的自定义主题 -->
        <div v-if="savedCustomThemes.length > 0">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">已保存的主题</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="theme in savedCustomThemes"
              :key="theme.name"
              class="theme-card border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 hover:shadow-lg relative"
              :class="currentTheme.name === theme.name 
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'"
              @click="selectTheme(theme)"
            >
              <!-- 删除按钮 -->
              <button
                @click.stop="deleteCustomTheme(theme)"
                class="absolute top-2 right-2 p-1 text-gray-400 hover:text-red-500 rounded"
              >
                <component :is="Trash2" class="w-4 h-4" />
              </button>
              
              <!-- 主题预览 -->
              <div 
                class="theme-preview rounded-lg p-3 mb-3 font-mono text-xs"
                :style="{ 
                  backgroundColor: theme.colors.background,
                  color: theme.colors.text
                }"
              >
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-2 h-2 rounded-full bg-red-500"></div>
                  <div class="w-2 h-2 rounded-full bg-yellow-500"></div>
                  <div class="w-2 h-2 rounded-full bg-green-500"></div>
                  <span class="ml-2 text-xs opacity-75">{{ theme.label }}</span>
                </div>
                <div class="space-y-1">
                  <div class="flex items-center space-x-2">
                    <span class="opacity-60">12:34:56</span>
                    <span 
                      class="px-1 rounded text-xs"
                      :style="{ backgroundColor: theme.colors.error, color: theme.colors.background }"
                    >
                      ERROR
                    </span>
                  </div>
                  <div>Custom theme preview</div>
                </div>
              </div>
              
              <!-- 主题信息 -->
              <div class="text-center">
                <h4 class="font-semibold text-gray-900 dark:text-white">{{ theme.label }}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">自定义主题</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Palette, X, Trash2 } from 'lucide-vue-next'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  currentTheme: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'themeChange'])

const showCustomEditor = ref(false)
const savedCustomThemes = ref([])

const presetThemes = [
  {
    name: 'dark',
    label: '暗黑经典',
    description: '经典的暗色终端主题',
    colors: {
      background: '#1f2937',
      text: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6',
      border: '#374151',
      headerBg: '#111827',
      contentBg: '#1f2937'
    }
  },
  {
    name: 'matrix',
    label: '黑客帝国',
    description: '绿色矩阵风格',
    colors: {
      background: '#000000',
      text: '#00ff00',
      error: '#ff0000',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#003300',
      headerBg: '#001100',
      contentBg: '#000000'
    }
  },
  {
    name: 'ocean',
    label: '深海蓝调',
    description: '深蓝色海洋主题',
    colors: {
      background: '#1e3a8a',
      text: '#93c5fd',
      error: '#fca5a5',
      warning: '#fcd34d',
      info: '#a7f3d0',
      border: '#1e40af',
      headerBg: '#1e40af',
      contentBg: '#1e3a8a'
    }
  },
  {
    name: 'sunset',
    label: '日落余晖',
    description: '温暖的橙色主题',
    colors: {
      background: '#9a3412',
      text: '#fed7aa',
      error: '#fecaca',
      warning: '#fef3c7',
      info: '#bfdbfe',
      border: '#c2410c',
      headerBg: '#c2410c',
      contentBg: '#9a3412'
    }
  },
  {
    name: 'cyberpunk',
    label: '赛博朋克',
    description: '未来科技风格',
    colors: {
      background: '#0f0f23',
      text: '#ff00ff',
      error: '#ff073a',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#ff00ff40',
      headerBg: '#1a1a2e',
      contentBg: '#0f0f23'
    }
  },
  {
    name: 'forest',
    label: '森林绿意',
    description: '自然绿色主题',
    colors: {
      background: '#064e3b',
      text: '#6ee7b7',
      error: '#f87171',
      warning: '#fbbf24',
      info: '#60a5fa',
      border: '#065f46',
      headerBg: '#065f46',
      contentBg: '#064e3b'
    }
  }
]

const customTheme = reactive({
  name: 'custom',
  label: '',
  colors: {
    background: '#1f2937',
    text: '#10b981',
    error: '#ef4444',
    warning: '#f59e0b',
    info: '#3b82f6',
    border: '#374151',
    headerBg: '#111827',
    contentBg: '#1f2937'
  }
})

const selectTheme = (theme) => {
  emit('themeChange', theme)
}

const saveCustomTheme = () => {
  if (!customTheme.label) {
    alert('请输入主题名称')
    return
  }
  
  const newTheme = {
    name: `custom_${Date.now()}`,
    label: customTheme.label,
    description: '自定义主题',
    colors: { ...customTheme.colors }
  }
  
  savedCustomThemes.value.push(newTheme)
  
  // 保存到localStorage
  localStorage.setItem('customTerminalThemes', JSON.stringify(savedCustomThemes.value))
  
  alert('主题已保存')
}

const applyCustomTheme = () => {
  const theme = {
    name: 'custom_temp',
    label: customTheme.label || '临时主题',
    colors: { ...customTheme.colors }
  }
  
  emit('themeChange', theme)
}

const deleteCustomTheme = (theme) => {
  const index = savedCustomThemes.value.findIndex(t => t.name === theme.name)
  if (index > -1) {
    savedCustomThemes.value.splice(index, 1)
    localStorage.setItem('customTerminalThemes', JSON.stringify(savedCustomThemes.value))
  }
}

// 加载保存的自定义主题
const loadSavedThemes = () => {
  const saved = localStorage.getItem('customTerminalThemes')
  if (saved) {
    savedCustomThemes.value = JSON.parse(saved)
  }
}

// 初始化
loadSavedThemes()
</script>

<style scoped>
.theme-card {
  transition: all 0.2s ease;
}

.theme-card:hover {
  transform: translateY(-2px);
}

.theme-preview {
  min-height: 120px;
}
</style>
