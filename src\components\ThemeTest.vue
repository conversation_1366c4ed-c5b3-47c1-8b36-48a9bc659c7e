<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6">主题切换测试</h1>
    
    <div class="mb-6">
      <h2 class="text-lg font-semibold mb-4">当前主题: {{ currentTheme.label }}</h2>
      <div class="flex space-x-2">
        <button
          v-for="theme in themes"
          :key="theme.name"
          @click="setTheme(theme)"
          class="px-4 py-2 rounded border"
          :class="currentTheme.name === theme.name ? 'bg-blue-500 text-white' : 'bg-gray-200'"
        >
          {{ theme.label }}
        </button>
      </div>
    </div>
    
    <!-- 主题预览 -->
    <div 
      class="p-6 rounded-lg border-2 transition-all duration-300"
      :style="previewStyles"
    >
      <h3 class="text-lg font-bold mb-4">主题预览</h3>
      
      <div class="space-y-3">
        <div class="flex items-center space-x-3">
          <span class="text-sm opacity-60">12:34:56</span>
          <span 
            class="px-2 py-1 text-xs font-bold rounded"
            :style="{ backgroundColor: currentTheme.colors.error, color: currentTheme.colors.background }"
          >
            ERROR
          </span>
          <span class="text-sm opacity-80">[nginx]</span>
          <span>Connection timeout error</span>
        </div>
        
        <div class="flex items-center space-x-3">
          <span class="text-sm opacity-60">12:34:57</span>
          <span 
            class="px-2 py-1 text-xs font-bold rounded"
            :style="{ backgroundColor: currentTheme.colors.warning, color: currentTheme.colors.background }"
          >
            WARN
          </span>
          <span class="text-sm opacity-80">[mysql]</span>
          <span>Slow query detected</span>
        </div>
        
        <div class="flex items-center space-x-3">
          <span class="text-sm opacity-60">12:34:58</span>
          <span 
            class="px-2 py-1 text-xs font-bold rounded"
            :style="{ backgroundColor: currentTheme.colors.info, color: currentTheme.colors.background }"
          >
            INFO
          </span>
          <span class="text-sm opacity-80">[api]</span>
          <span>Request processed successfully</span>
        </div>
      </div>
    </div>
    
    <!-- 颜色信息 -->
    <div class="mt-6">
      <h3 class="text-lg font-semibold mb-4">颜色信息</h3>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div v-for="(color, key) in currentTheme.colors" :key="key" class="text-center">
          <div 
            class="w-16 h-16 rounded-lg border mx-auto mb-2"
            :style="{ backgroundColor: color }"
          ></div>
          <div class="text-sm font-medium">{{ key }}</div>
          <div class="text-xs text-gray-600 font-mono">{{ color }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const themes = [
  {
    name: 'dark',
    label: '暗黑经典',
    colors: {
      background: '#1f2937',
      text: '#10b981',
      error: '#ef4444',
      warning: '#f59e0b',
      info: '#3b82f6',
      border: '#374151',
      headerBg: '#111827',
      contentBg: '#1f2937'
    }
  },
  {
    name: 'matrix',
    label: '黑客帝国',
    colors: {
      background: '#000000',
      text: '#00ff00',
      error: '#ff0000',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#003300',
      headerBg: '#001100',
      contentBg: '#000000'
    }
  },
  {
    name: 'ocean',
    label: '深海蓝调',
    colors: {
      background: '#1e3a8a',
      text: '#93c5fd',
      error: '#fca5a5',
      warning: '#fcd34d',
      info: '#a7f3d0',
      border: '#1e40af',
      headerBg: '#1e40af',
      contentBg: '#1e3a8a'
    }
  },
  {
    name: 'sunset',
    label: '日落余晖',
    colors: {
      background: '#9a3412',
      text: '#fed7aa',
      error: '#fecaca',
      warning: '#fef3c7',
      info: '#bfdbfe',
      border: '#c2410c',
      headerBg: '#c2410c',
      contentBg: '#9a3412'
    }
  },
  {
    name: 'cyberpunk',
    label: '赛博朋克',
    colors: {
      background: '#0f0f23',
      text: '#ff00ff',
      error: '#ff073a',
      warning: '#ffff00',
      info: '#00ffff',
      border: '#ff00ff40',
      headerBg: '#1a1a2e',
      contentBg: '#0f0f23'
    }
  }
]

const currentTheme = ref(themes[0])

const previewStyles = computed(() => {
  const colors = currentTheme.value.colors
  return {
    backgroundColor: colors.background,
    color: colors.text,
    borderColor: colors.border,
    background: `linear-gradient(135deg, ${colors.background} 0%, ${colors.contentBg} 100%)`
  }
})

const setTheme = (theme) => {
  currentTheme.value = theme
  console.log('主题已切换到:', theme.label, theme.colors)
}
</script>
