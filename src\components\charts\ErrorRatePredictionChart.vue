<template>
  <div class="h-full">
    <Line
      :data="chartData"
      :options="chartOptions"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Line } from 'vue-chartjs'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartData = computed(() => ({
  labels: props.data.map(item => {
    const date = new Date(item.time)
    return date.toLocaleDateString('zh-CN', { 
      month: 'short', 
      day: 'numeric' 
    })
  }),
  datasets: [
    {
      label: '历史错误率',
      data: props.data.map(item => item.actual || null),
      borderColor: '#ef4444',
      backgroundColor: 'rgba(239, 68, 68, 0.1)',
      fill: false,
      tension: 0.4,
      pointRadius: 2,
      pointHoverRadius: 4,
      spanGaps: false
    },
    {
      label: '预测错误率',
      data: props.data.map(item => item.predicted || null),
      borderColor: '#f97316',
      backgroundColor: 'rgba(249, 115, 22, 0.1)',
      borderDash: [5, 5],
      fill: false,
      tension: 0.4,
      pointRadius: 2,
      pointHoverRadius: 4,
      spanGaps: false
    },
    {
      label: '置信区间上限',
      data: props.data.map(item => item.upper || null),
      borderColor: 'rgba(249, 115, 22, 0.3)',
      backgroundColor: 'rgba(249, 115, 22, 0.1)',
      fill: '+1',
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 0,
      spanGaps: false
    },
    {
      label: '置信区间下限',
      data: props.data.map(item => item.lower || null),
      borderColor: 'rgba(249, 115, 22, 0.3)',
      backgroundColor: 'rgba(249, 115, 22, 0.1)',
      fill: false,
      tension: 0.4,
      pointRadius: 0,
      pointHoverRadius: 0,
      spanGaps: false
    }
  ]
}))

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top',
      labels: {
        usePointStyle: true,
        padding: 20,
        filter: (legendItem) => {
          return !legendItem.text.includes('置信区间')
        }
      }
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#374151',
      borderWidth: 1,
      callbacks: {
        label: (context) => {
          const label = context.dataset.label
          const value = context.parsed.y
          return value !== null ? `${label}: ${value.toFixed(2)}%` : null
        }
      }
    }
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: false
      },
      ticks: {
        maxTicksLimit: 10,
        color: '#6b7280'
      }
    },
    y: {
      display: true,
      beginAtZero: true,
      grid: {
        color: 'rgba(156, 163, 175, 0.2)'
      },
      ticks: {
        color: '#6b7280',
        callback: function(value) {
          return value + '%'
        }
      }
    }
  },
  interaction: {
    mode: 'nearest',
    axis: 'x',
    intersect: false
  }
}))
</script>
