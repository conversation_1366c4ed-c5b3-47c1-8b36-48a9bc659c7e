<template>
  <div class="h-full">
    <Bar
      :data="chartData"
      :options="chartOptions"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Bar } from 'vue-chartjs'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartData = computed(() => ({
  labels: props.data.map(item => item.country),
  datasets: [
    {
      label: '威胁数量',
      data: props.data.map(item => item.threats),
      backgroundColor: props.data.map(item => {
        if (item.threats > 200) return 'rgba(239, 68, 68, 0.8)'
        if (item.threats > 150) return 'rgba(245, 158, 11, 0.8)'
        return 'rgba(59, 130, 246, 0.8)'
      }),
      borderColor: props.data.map(item => {
        if (item.threats > 200) return '#ef4444'
        if (item.threats > 150) return '#f59e0b'
        return '#3b82f6'
      }),
      borderWidth: 2,
      borderRadius: 4,
      borderSkipped: false
    }
  ]
}))

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  indexAxis: 'y',
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#374151',
      borderWidth: 1,
      callbacks: {
        label: (context) => `威胁数量: ${context.parsed.x}`
      }
    }
  },
  scales: {
    x: {
      display: true,
      beginAtZero: true,
      grid: {
        color: 'rgba(156, 163, 175, 0.2)'
      },
      ticks: {
        color: '#6b7280'
      }
    },
    y: {
      display: true,
      grid: {
        display: false
      },
      ticks: {
        color: '#6b7280'
      }
    }
  }
}))
</script>
