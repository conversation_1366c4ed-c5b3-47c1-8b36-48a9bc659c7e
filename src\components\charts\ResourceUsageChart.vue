<template>
  <div class="h-full">
    <Bar
      :data="chartData"
      :options="chartOptions"
    />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Bar } from 'vue-chartjs'

const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const chartData = computed(() => ({
  labels: props.data.map(item => item.name),
  datasets: [
    {
      label: '使用率 (%)',
      data: props.data.map(item => item.value),
      backgroundColor: props.data.map(item => {
        if (item.value > 80) return 'rgba(239, 68, 68, 0.8)'
        if (item.value > 60) return 'rgba(245, 158, 11, 0.8)'
        return 'rgba(16, 185, 129, 0.8)'
      }),
      borderColor: props.data.map(item => {
        if (item.value > 80) return '#ef4444'
        if (item.value > 60) return '#f59e0b'
        return '#10b981'
      }),
      borderWidth: 2,
      borderRadius: 4,
      borderSkipped: false
    }
  ]
}))

const chartOptions = computed(() => ({
  responsive: true,
  maintainAspectRatio: false,
  animation: {
    duration: 750,
    easing: 'easeInOutQuart'
  },
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      titleColor: '#fff',
      bodyColor: '#fff',
      borderColor: '#374151',
      borderWidth: 1,
      callbacks: {
        label: (context) => `${context.dataset.label}: ${context.parsed.y}%`
      }
    }
  },
  scales: {
    x: {
      display: true,
      grid: {
        display: false
      },
      ticks: {
        color: '#6b7280'
      }
    },
    y: {
      display: true,
      beginAtZero: true,
      max: 100,
      grid: {
        color: 'rgba(156, 163, 175, 0.2)'
      },
      ticks: {
        color: '#6b7280',
        callback: function(value) {
          return value + '%'
        }
      }
    }
  }
}))
</script>
