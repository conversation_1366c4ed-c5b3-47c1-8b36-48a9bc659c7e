<template>
  <div class="h-full flex items-center justify-center">
    <div class="flex flex-wrap items-center justify-center gap-2 p-4">
      <span
        v-for="tag in data"
        :key="tag.text"
        class="inline-block px-3 py-1 rounded-full font-medium cursor-pointer hover:opacity-80 transition-opacity"
        :style="{
          fontSize: `${tag.size}px`,
          backgroundColor: tag.color,
          color: getContrastColor(tag.color)
        }"
      >
        {{ tag.text }}
      </span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  data: {
    type: Array,
    required: true
  }
})

const getContrastColor = (hexColor) => {
  // 简单的对比度计算
  const r = parseInt(hexColor.slice(1, 3), 16)
  const g = parseInt(hexColor.slice(3, 5), 16)
  const b = parseInt(hexColor.slice(5, 7), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}
</script>
