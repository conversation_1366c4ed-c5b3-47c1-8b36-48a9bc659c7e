import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '@/views/Dashboard.vue'
import <PERSON>g<PERSON>iewer from '@/views/LogViewer.vue'
import Analytics from '@/views/Analytics.vue'
import Settings from '@/views/Settings.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard,
    meta: {
      title: '仪表板',
      icon: 'BarChart3'
    }
  },
  {
    path: '/logs',
    name: '<PERSON>g<PERSON>ie<PERSON>',
    component: LogViewer,
    meta: {
      title: '日志查看器',
      icon: 'FileText'
    }
  },
  {
    path: '/analytics',
    name: 'Analytics',
    component: Analytics,
    meta: {
      title: '数据分析',
      icon: 'TrendingUp'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '系统设置',
      icon: 'Settings'
    }
  },
  {
    path: '/live-logs',
    name: 'LiveLogs',
    component: () => import('@/views/LiveLogs.vue'),
    meta: {
      title: '实时日志终端',
      icon: 'Terminal'
    }
  },
  {
    path: '/theme-test',
    name: 'ThemeTest',
    component: () => import('@/components/ThemeTest.vue'),
    meta: {
      title: '主题测试',
      icon: 'Palette'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title} - 高级日志管理系统`
  next()
})

export default router
