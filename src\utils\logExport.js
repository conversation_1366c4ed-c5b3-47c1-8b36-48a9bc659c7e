// 日志导出工具

/**
 * 导出日志为JSON格式
 * @param {Array} logs - 日志数组
 * @param {string} filename - 文件名
 */
export function exportLogsAsJSON(logs, filename = 'logs') {
  const data = {
    exportTime: new Date().toISOString(),
    totalCount: logs.length,
    logs: logs
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  downloadBlob(blob, `${filename}.json`)
}

/**
 * 导出日志为CSV格式
 * @param {Array} logs - 日志数组
 * @param {string} filename - 文件名
 */
export function exportLogsAsCSV(logs, filename = 'logs') {
  const headers = ['时间戳', '级别', '来源', '服务', '消息', '用户ID', '请求ID', 'IP地址', '响应时间']
  
  const csvContent = [
    headers.join(','),
    ...logs.map(log => [
      `"${log.timestamp}"`,
      `"${log.level}"`,
      `"${log.source}"`,
      `"${log.service}"`,
      `"${log.message.replace(/"/g, '""')}"`, // 转义双引号
      `"${log.metadata?.userId || ''}"`,
      `"${log.metadata?.requestId || ''}"`,
      `"${log.metadata?.ip || ''}"`,
      `"${log.metadata?.responseTime || ''}"`
    ].join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  downloadBlob(blob, `${filename}.csv`)
}

/**
 * 导出日志为纯文本格式
 * @param {Array} logs - 日志数组
 * @param {string} filename - 文件名
 */
export function exportLogsAsText(logs, filename = 'logs') {
  const textContent = logs.map(log => {
    const timestamp = new Date(log.timestamp).toISOString()
    const metadata = log.metadata ? ` [${Object.entries(log.metadata).map(([k, v]) => `${k}=${v}`).join(', ')}]` : ''
    return `[${timestamp}] ${log.level.toUpperCase()} ${log.source}/${log.service}${metadata}: ${log.message}`
  }).join('\n')
  
  const blob = new Blob([textContent], { type: 'text/plain;charset=utf-8;' })
  downloadBlob(blob, `${filename}.txt`)
}

/**
 * 导出日志为Excel格式 (实际是CSV，但Excel可以打开)
 * @param {Array} logs - 日志数组
 * @param {string} filename - 文件名
 */
export function exportLogsAsExcel(logs, filename = 'logs') {
  const headers = ['时间戳', '级别', '来源', '服务', '消息', '用户ID', '请求ID', 'IP地址', '响应时间']
  
  // 添加BOM以支持中文
  const BOM = '\uFEFF'
  const csvContent = BOM + [
    headers.join('\t'), // 使用制表符分隔，Excel更好识别
    ...logs.map(log => [
      log.timestamp,
      log.level,
      log.source,
      log.service,
      log.message.replace(/\t/g, ' '), // 移除制表符避免格式问题
      log.metadata?.userId || '',
      log.metadata?.requestId || '',
      log.metadata?.ip || '',
      log.metadata?.responseTime || ''
    ].join('\t'))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel;charset=utf-8;' })
  downloadBlob(blob, `${filename}.xls`)
}

/**
 * 导出过滤后的日志统计报告
 * @param {Array} logs - 日志数组
 * @param {object} filters - 当前过滤条件
 * @param {string} filename - 文件名
 */
export function exportLogReport(logs, filters, filename = 'log-report') {
  const stats = generateLogStats(logs)
  
  const report = {
    reportTime: new Date().toISOString(),
    filters: filters,
    summary: stats,
    logs: logs
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
  downloadBlob(blob, `${filename}.json`)
}

/**
 * 生成日志统计信息
 * @param {Array} logs - 日志数组
 * @returns {object} - 统计信息
 */
function generateLogStats(logs) {
  const stats = {
    total: logs.length,
    levels: {},
    sources: {},
    services: {},
    timeRange: {
      start: null,
      end: null
    },
    errors: [],
    topSources: [],
    topServices: []
  }
  
  // 统计级别分布
  logs.forEach(log => {
    stats.levels[log.level] = (stats.levels[log.level] || 0) + 1
    stats.sources[log.source] = (stats.sources[log.source] || 0) + 1
    stats.services[log.service] = (stats.services[log.service] || 0) + 1
    
    // 收集错误日志
    if (log.level === 'error') {
      stats.errors.push({
        timestamp: log.timestamp,
        source: log.source,
        service: log.service,
        message: log.message
      })
    }
    
    // 更新时间范围
    const timestamp = new Date(log.timestamp)
    if (!stats.timeRange.start || timestamp < new Date(stats.timeRange.start)) {
      stats.timeRange.start = log.timestamp
    }
    if (!stats.timeRange.end || timestamp > new Date(stats.timeRange.end)) {
      stats.timeRange.end = log.timestamp
    }
  })
  
  // 排序获取Top来源和服务
  stats.topSources = Object.entries(stats.sources)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([name, count]) => ({ name, count }))
    
  stats.topServices = Object.entries(stats.services)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([name, count]) => ({ name, count }))
  
  return stats
}

/**
 * 下载Blob文件
 * @param {Blob} blob - 文件内容
 * @param {string} filename - 文件名
 */
function downloadBlob(blob, filename) {
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

/**
 * 批量导出多种格式
 * @param {Array} logs - 日志数组
 * @param {Array} formats - 导出格式数组 ['json', 'csv', 'txt', 'excel']
 * @param {string} baseFilename - 基础文件名
 */
export function exportLogsMultipleFormats(logs, formats, baseFilename = 'logs') {
  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-')
  const filename = `${baseFilename}-${timestamp}`
  
  formats.forEach(format => {
    switch (format) {
      case 'json':
        exportLogsAsJSON(logs, filename)
        break
      case 'csv':
        exportLogsAsCSV(logs, filename)
        break
      case 'txt':
        exportLogsAsText(logs, filename)
        break
      case 'excel':
        exportLogsAsExcel(logs, filename)
        break
    }
  })
}

/**
 * 导出日志为压缩包 (需要JSZip库)
 * @param {Array} logs - 日志数组
 * @param {string} filename - 文件名
 */
export async function exportLogsAsZip(logs, filename = 'logs') {
  // 这里需要JSZip库，暂时提供接口
  console.log('ZIP导出功能需要JSZip库支持')
  
  // 示例实现：
  // const JSZip = await import('jszip')
  // const zip = new JSZip.default()
  // 
  // // 添加不同格式的文件到压缩包
  // zip.file(`${filename}.json`, JSON.stringify(logs, null, 2))
  // zip.file(`${filename}.csv`, generateCSVContent(logs))
  // zip.file(`${filename}.txt`, generateTextContent(logs))
  // 
  // const content = await zip.generateAsync({ type: 'blob' })
  // downloadBlob(content, `${filename}.zip`)
}
