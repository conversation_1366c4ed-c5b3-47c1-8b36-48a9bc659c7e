// 日志语法高亮工具

/**
 * 高亮日志消息中的关键信息
 * @param {string} message - 日志消息
 * @param {string} searchTerm - 搜索词
 * @returns {string} - 高亮后的HTML
 */
export function highlightLogMessage(message, searchTerm = '') {
  if (!message) return ''
  
  let highlighted = message
  
  // 1. 高亮搜索词
  if (searchTerm && searchTerm.trim()) {
    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi')
    highlighted = highlighted.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100">$1</mark>')
  }
  
  // 2. 高亮IP地址
  highlighted = highlighted.replace(
    /\b(?:\d{1,3}\.){3}\d{1,3}\b/g,
    '<span class="text-blue-600 dark:text-blue-400 font-mono">$&</span>'
  )
  
  // 3. 高亮URL
  highlighted = highlighted.replace(
    /https?:\/\/[^\s]+/g,
    '<span class="text-indigo-600 dark:text-indigo-400 underline">$&</span>'
  )
  
  // 4. 高亮时间戳
  highlighted = highlighted.replace(
    /\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})?/g,
    '<span class="text-gray-600 dark:text-gray-400 font-mono">$&</span>'
  )
  
  // 5. 高亮状态码
  highlighted = highlighted.replace(
    /\b[1-5]\d{2}\b/g,
    (match) => {
      const code = parseInt(match)
      let colorClass = 'text-gray-600 dark:text-gray-400'
      
      if (code >= 200 && code < 300) {
        colorClass = 'text-green-600 dark:text-green-400'
      } else if (code >= 300 && code < 400) {
        colorClass = 'text-yellow-600 dark:text-yellow-400'
      } else if (code >= 400 && code < 500) {
        colorClass = 'text-orange-600 dark:text-orange-400'
      } else if (code >= 500) {
        colorClass = 'text-red-600 dark:text-red-400'
      }
      
      return `<span class="${colorClass} font-semibold">${match}</span>`
    }
  )
  
  // 6. 高亮JSON对象
  highlighted = highlighted.replace(
    /\{[^{}]*\}/g,
    '<span class="text-purple-600 dark:text-purple-400 font-mono">$&</span>'
  )
  
  // 7. 高亮数字
  highlighted = highlighted.replace(
    /\b\d+(?:\.\d+)?\b/g,
    '<span class="text-cyan-600 dark:text-cyan-400 font-mono">$&</span>'
  )
  
  // 8. 高亮错误关键词
  highlighted = highlighted.replace(
    /\b(error|exception|failed|failure|timeout|denied|forbidden|unauthorized|invalid|null|undefined)\b/gi,
    '<span class="text-red-600 dark:text-red-400 font-semibold">$&</span>'
  )
  
  // 9. 高亮成功关键词
  highlighted = highlighted.replace(
    /\b(success|successful|completed|ok|done|finished|created|updated|deleted)\b/gi,
    '<span class="text-green-600 dark:text-green-400 font-semibold">$&</span>'
  )
  
  // 10. 高亮警告关键词
  highlighted = highlighted.replace(
    /\b(warning|warn|deprecated|slow|retry|fallback)\b/gi,
    '<span class="text-yellow-600 dark:text-yellow-400 font-semibold">$&</span>'
  )
  
  return highlighted
}

/**
 * 转义正则表达式特殊字符
 * @param {string} string - 要转义的字符串
 * @returns {string} - 转义后的字符串
 */
function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

/**
 * 检测日志级别并返回对应的样式类
 * @param {string} level - 日志级别
 * @returns {object} - 样式配置
 */
export function getLogLevelStyle(level) {
  const styles = {
    error: {
      color: 'bg-red-500',
      badge: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
      border: 'border-red-200 dark:border-red-800'
    },
    warn: {
      color: 'bg-yellow-500',
      badge: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      border: 'border-yellow-200 dark:border-yellow-800'
    },
    info: {
      color: 'bg-blue-500',
      badge: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      border: 'border-blue-200 dark:border-blue-800'
    },
    debug: {
      color: 'bg-gray-500',
      badge: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      border: 'border-gray-200 dark:border-gray-800'
    },
    trace: {
      color: 'bg-purple-500',
      badge: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      border: 'border-purple-200 dark:border-purple-800'
    }
  }
  
  return styles[level] || styles.info
}

/**
 * 格式化日志消息，添加缩进和换行
 * @param {string} message - 原始消息
 * @returns {string} - 格式化后的消息
 */
export function formatLogMessage(message) {
  if (!message) return ''
  
  try {
    // 尝试解析JSON并格式化
    const parsed = JSON.parse(message)
    return JSON.stringify(parsed, null, 2)
  } catch {
    // 如果不是JSON，进行基本格式化
    return message
      .replace(/\t/g, '  ') // 制表符转空格
      .replace(/\\n/g, '\n') // 转义换行符
      .trim()
  }
}

/**
 * 提取日志消息中的关键信息
 * @param {string} message - 日志消息
 * @returns {object} - 提取的信息
 */
export function extractLogInfo(message) {
  const info = {
    ips: [],
    urls: [],
    statusCodes: [],
    timestamps: [],
    errors: [],
    json: []
  }
  
  // 提取IP地址
  const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/g
  info.ips = [...(message.match(ipRegex) || [])]
  
  // 提取URL
  const urlRegex = /https?:\/\/[^\s]+/g
  info.urls = [...(message.match(urlRegex) || [])]
  
  // 提取状态码
  const statusRegex = /\b[1-5]\d{2}\b/g
  info.statusCodes = [...(message.match(statusRegex) || [])].map(Number)
  
  // 提取时间戳
  const timestampRegex = /\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})?/g
  info.timestamps = [...(message.match(timestampRegex) || [])]
  
  // 提取错误关键词
  const errorRegex = /\b(error|exception|failed|failure|timeout|denied|forbidden|unauthorized|invalid|null|undefined)\b/gi
  info.errors = [...(message.match(errorRegex) || [])]
  
  // 提取JSON对象
  const jsonRegex = /\{[^{}]*\}/g
  info.json = [...(message.match(jsonRegex) || [])]
  
  return info
}
