/**
 * 高级日志解析器
 * 支持多种日志格式的智能解析
 */

// 预定义的日志格式模式
const LOG_PATTERNS = {
  // Apache访问日志
  apache: {
    pattern: /^(\S+) \S+ \S+ \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+|-) "([^"]*)" "([^"]*)"/,
    fields: ['ip', 'timestamp', 'method', 'url', 'status', 'size', 'referer', 'userAgent'],
    timestampFormat: 'DD/MMM/YYYY:HH:mm:ss Z'
  },
  
  // Nginx访问日志
  nginx: {
    pattern: /^(\S+) - (\S+) \[([^\]]+)\] "(\S+) ([^"]*)" (\d+) (\d+) "([^"]*)" "([^"]*)" "([^"]*)"/,
    fields: ['ip', 'user', 'timestamp', 'method', 'url', 'status', 'size', 'referer', 'userAgent', 'forwardedFor'],
    timestampFormat: 'DD/MMM/YYYY:HH:mm:ss Z'
  },
  
  // 标准应用日志
  application: {
    pattern: /^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}[.,]\d{3}) \[(\w+)\] \[([^\]]*)\] (.*)$/,
    fields: ['timestamp', 'level', 'thread', 'message'],
    timestampFormat: 'YYYY-MM-DD HH:mm:ss.SSS'
  },
  
  // JSON格式日志
  json: {
    pattern: /^{.*}$/,
    fields: ['raw'],
    parser: 'json'
  },
  
  // Syslog格式
  syslog: {
    pattern: /^(\w{3} \d{1,2} \d{2}:\d{2}:\d{2}) (\S+) (\S+)(?:\[(\d+)\])?: (.*)$/,
    fields: ['timestamp', 'hostname', 'program', 'pid', 'message'],
    timestampFormat: 'MMM DD HH:mm:ss'
  },
  
  // Docker容器日志
  docker: {
    pattern: /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) (\S+) (.*)$/,
    fields: ['timestamp', 'stream', 'message'],
    timestampFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
  },
  
  // Kubernetes日志
  kubernetes: {
    pattern: /^(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+Z) (\w+) (\S+) (\S+) (.*)$/,
    fields: ['timestamp', 'level', 'namespace', 'pod', 'message'],
    timestampFormat: 'YYYY-MM-DDTHH:mm:ss.SSSZ'
  }
}

// 日志级别映射
const LOG_LEVELS = {
  'TRACE': { priority: 0, color: '#6b7280', bgColor: '#f9fafb' },
  'DEBUG': { priority: 1, color: '#6b7280', bgColor: '#f3f4f6' },
  'INFO': { priority: 2, color: '#2563eb', bgColor: '#dbeafe' },
  'WARN': { priority: 3, color: '#d97706', bgColor: '#fef3c7' },
  'WARNING': { priority: 3, color: '#d97706', bgColor: '#fef3c7' },
  'ERROR': { priority: 4, color: '#dc2626', bgColor: '#fee2e2' },
  'FATAL': { priority: 5, color: '#991b1b', bgColor: '#fecaca' },
  'CRITICAL': { priority: 5, color: '#991b1b', bgColor: '#fecaca' }
}

// IP地址正则
const IP_REGEX = /\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/g

// URL正则
const URL_REGEX = /https?:\/\/[^\s]+/g

// 邮箱正则
const EMAIL_REGEX = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g

// 时间戳正则
const TIMESTAMP_REGEX = /\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})?/g

/**
 * 智能检测日志格式
 */
export function detectLogFormat(logLine) {
  for (const [format, config] of Object.entries(LOG_PATTERNS)) {
    if (config.pattern.test(logLine)) {
      return format
    }
  }
  return 'unknown'
}

/**
 * 解析单行日志
 */
export function parseLogLine(logLine, format = null) {
  // 自动检测格式
  if (!format) {
    format = detectLogFormat(logLine)
  }
  
  const config = LOG_PATTERNS[format]
  if (!config) {
    return parseGenericLog(logLine)
  }
  
  // JSON格式特殊处理
  if (config.parser === 'json') {
    return parseJsonLog(logLine)
  }
  
  // 正则匹配解析
  const match = logLine.match(config.pattern)
  if (!match) {
    return parseGenericLog(logLine)
  }
  
  const parsed = {
    raw: logLine,
    format: format,
    fields: {}
  }
  
  // 提取字段
  config.fields.forEach((field, index) => {
    parsed.fields[field] = match[index + 1] || null
  })
  
  // 标准化处理
  return normalizeLogEntry(parsed)
}

/**
 * 解析JSON格式日志
 */
function parseJsonLog(logLine) {
  try {
    const jsonData = JSON.parse(logLine)
    
    return {
      raw: logLine,
      format: 'json',
      fields: {
        timestamp: jsonData.timestamp || jsonData.time || jsonData['@timestamp'],
        level: jsonData.level || jsonData.severity,
        message: jsonData.message || jsonData.msg,
        source: jsonData.source || jsonData.logger,
        ...jsonData
      }
    }
  } catch (error) {
    return parseGenericLog(logLine)
  }
}

/**
 * 通用日志解析
 */
function parseGenericLog(logLine) {
  const parsed = {
    raw: logLine,
    format: 'generic',
    fields: {
      message: logLine
    }
  }
  
  // 尝试提取时间戳
  const timestampMatch = logLine.match(TIMESTAMP_REGEX)
  if (timestampMatch) {
    parsed.fields.timestamp = timestampMatch[0]
  }
  
  // 尝试提取日志级别
  const levelMatch = logLine.match(/\b(TRACE|DEBUG|INFO|WARN|WARNING|ERROR|FATAL|CRITICAL)\b/i)
  if (levelMatch) {
    parsed.fields.level = levelMatch[1].toUpperCase()
  }
  
  return normalizeLogEntry(parsed)
}

/**
 * 标准化日志条目
 */
function normalizeLogEntry(parsed) {
  const normalized = {
    ...parsed,
    id: generateLogId(),
    timestamp: parseTimestamp(parsed.fields.timestamp),
    level: normalizeLevel(parsed.fields.level),
    message: parsed.fields.message || parsed.raw,
    source: parsed.fields.source || parsed.fields.program || 'unknown',
    metadata: extractMetadata(parsed.raw)
  }
  
  // 添加级别信息
  if (normalized.level && LOG_LEVELS[normalized.level]) {
    normalized.levelInfo = LOG_LEVELS[normalized.level]
  }
  
  return normalized
}

/**
 * 解析时间戳
 */
function parseTimestamp(timestamp) {
  if (!timestamp) {
    return new Date()
  }
  
  // 尝试直接解析
  const date = new Date(timestamp)
  if (!isNaN(date.getTime())) {
    return date
  }
  
  // 尝试其他格式
  // 这里可以添加更多时间格式的解析逻辑
  
  return new Date()
}

/**
 * 标准化日志级别
 */
function normalizeLevel(level) {
  if (!level) return 'INFO'
  
  const upperLevel = level.toUpperCase()
  
  // 映射常见的级别别名
  const levelMap = {
    'WARN': 'WARNING',
    'ERR': 'ERROR',
    'CRIT': 'CRITICAL',
    'EMERG': 'CRITICAL',
    'ALERT': 'CRITICAL'
  }
  
  return levelMap[upperLevel] || upperLevel
}

/**
 * 提取元数据
 */
function extractMetadata(logLine) {
  const metadata = {}
  
  // 提取IP地址
  const ips = logLine.match(IP_REGEX)
  if (ips) {
    metadata.ips = [...new Set(ips)]
  }
  
  // 提取URL
  const urls = logLine.match(URL_REGEX)
  if (urls) {
    metadata.urls = [...new Set(urls)]
  }
  
  // 提取邮箱
  const emails = logLine.match(EMAIL_REGEX)
  if (emails) {
    metadata.emails = [...new Set(emails)]
  }
  
  // 提取HTTP状态码
  const statusMatch = logLine.match(/\b[1-5]\d{2}\b/)
  if (statusMatch) {
    metadata.httpStatus = parseInt(statusMatch[0])
  }
  
  // 提取响应时间
  const responseTimeMatch = logLine.match(/(\d+(?:\.\d+)?)\s*ms/)
  if (responseTimeMatch) {
    metadata.responseTime = parseFloat(responseTimeMatch[1])
  }
  
  // 提取文件路径
  const pathMatch = logLine.match(/\/[^\s]+/)
  if (pathMatch) {
    metadata.paths = [...new Set(pathMatch)]
  }
  
  return metadata
}

/**
 * 生成唯一日志ID
 */
function generateLogId() {
  return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 批量解析日志
 */
export function parseLogBatch(logLines, format = null) {
  return logLines.map(line => parseLogLine(line, format))
}

/**
 * 日志过滤器
 */
export function filterLogs(logs, filters) {
  return logs.filter(log => {
    // 级别过滤
    if (filters.level && log.level !== filters.level) {
      return false
    }
    
    // 最小级别过滤
    if (filters.minLevel) {
      const logPriority = LOG_LEVELS[log.level]?.priority || 0
      const minPriority = LOG_LEVELS[filters.minLevel]?.priority || 0
      if (logPriority < minPriority) {
        return false
      }
    }
    
    // 来源过滤
    if (filters.source && log.source !== filters.source) {
      return false
    }
    
    // 关键词过滤
    if (filters.keyword) {
      const keyword = filters.keyword.toLowerCase()
      if (!log.message.toLowerCase().includes(keyword)) {
        return false
      }
    }
    
    // 时间范围过滤
    if (filters.startTime && log.timestamp < filters.startTime) {
      return false
    }
    
    if (filters.endTime && log.timestamp > filters.endTime) {
      return false
    }
    
    // IP过滤
    if (filters.ip && (!log.metadata.ips || !log.metadata.ips.includes(filters.ip))) {
      return false
    }
    
    // HTTP状态码过滤
    if (filters.httpStatus && log.metadata.httpStatus !== filters.httpStatus) {
      return false
    }
    
    return true
  })
}

/**
 * 日志聚合统计
 */
export function aggregateLogs(logs) {
  const stats = {
    total: logs.length,
    byLevel: {},
    bySource: {},
    byHour: {},
    errors: [],
    warnings: [],
    topIps: {},
    topUrls: {},
    avgResponseTime: 0,
    httpStatusCodes: {}
  }
  
  let totalResponseTime = 0
  let responseTimeCount = 0
  
  logs.forEach(log => {
    // 按级别统计
    stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1
    
    // 按来源统计
    stats.bySource[log.source] = (stats.bySource[log.source] || 0) + 1
    
    // 按小时统计
    const hour = log.timestamp.getHours()
    stats.byHour[hour] = (stats.byHour[hour] || 0) + 1
    
    // 收集错误和警告
    if (log.level === 'ERROR' || log.level === 'FATAL' || log.level === 'CRITICAL') {
      stats.errors.push(log)
    } else if (log.level === 'WARNING' || log.level === 'WARN') {
      stats.warnings.push(log)
    }
    
    // IP统计
    if (log.metadata.ips) {
      log.metadata.ips.forEach(ip => {
        stats.topIps[ip] = (stats.topIps[ip] || 0) + 1
      })
    }
    
    // URL统计
    if (log.metadata.urls) {
      log.metadata.urls.forEach(url => {
        stats.topUrls[url] = (stats.topUrls[url] || 0) + 1
      })
    }
    
    // 响应时间统计
    if (log.metadata.responseTime) {
      totalResponseTime += log.metadata.responseTime
      responseTimeCount++
    }
    
    // HTTP状态码统计
    if (log.metadata.httpStatus) {
      stats.httpStatusCodes[log.metadata.httpStatus] = 
        (stats.httpStatusCodes[log.metadata.httpStatus] || 0) + 1
    }
  })
  
  // 计算平均响应时间
  if (responseTimeCount > 0) {
    stats.avgResponseTime = totalResponseTime / responseTimeCount
  }
  
  // 排序统计结果
  stats.topIps = Object.entries(stats.topIps)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .reduce((obj, [ip, count]) => ({ ...obj, [ip]: count }), {})
  
  stats.topUrls = Object.entries(stats.topUrls)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .reduce((obj, [url, count]) => ({ ...obj, [url]: count }), {})
  
  return stats
}

/**
 * 导出日志格式
 */
export function exportLogs(logs, format = 'json') {
  switch (format) {
    case 'json':
      return JSON.stringify(logs, null, 2)
    
    case 'csv':
      return exportToCsv(logs)
    
    case 'txt':
      return logs.map(log => log.raw).join('\n')
    
    default:
      return JSON.stringify(logs, null, 2)
  }
}

/**
 * 导出为CSV格式
 */
function exportToCsv(logs) {
  if (logs.length === 0) return ''
  
  const headers = ['timestamp', 'level', 'source', 'message']
  const csvLines = [headers.join(',')]
  
  logs.forEach(log => {
    const row = [
      log.timestamp.toISOString(),
      log.level,
      log.source,
      `"${log.message.replace(/"/g, '""')}"`
    ]
    csvLines.push(row.join(','))
  })
  
  return csvLines.join('\n')
}

export { LOG_PATTERNS, LOG_LEVELS }
