<template>
  <div class="space-y-6">
    <!-- 分析导航标签 -->
    <div class="card p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white">日志分析</h2>
        <div class="flex items-center space-x-4">
          <select
            v-model="selectedTimeRange"
            class="input w-40"
          >
            <option value="1h">最近1小时</option>
            <option value="6h">最近6小时</option>
            <option value="24h">最近24小时</option>
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
          </select>
          <button
            @click="refreshAnalytics"
            class="btn-primary"
          >
            <component :is="RefreshCw" class="w-4 h-4 mr-2" />
            刷新
          </button>
        </div>
      </div>

      <!-- 分析模块标签 -->
      <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
        <button
          v-for="tab in analyticsTabs"
          :key="tab.key"
          @click="activeTab = tab.key"
          class="flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors"
          :class="activeTab === tab.key
            ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
            : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'"
        >
          <component :is="tab.icon" class="w-4 h-4 mr-2 inline" />
          {{ tab.label }}
        </button>
      </div>
    </div>
    
    <!-- 基础分析 -->
    <div v-if="activeTab === 'overview'">
      <!-- Key Metrics -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <MetricCard
        title="错误率"
        :value="errorRate + '%'"
        :trend="{ value: 2.3, direction: 'down' }"
        color="red"
        icon="AlertCircle"
      />
      <MetricCard
        title="平均响应时间"
        :value="avgResponseTime + 'ms'"
        :trend="{ value: 5.1, direction: 'up' }"
        color="blue"
        icon="Clock"
      />
      <MetricCard
        title="吞吐量"
        :value="throughput + '/分钟'"
        :trend="{ value: 12.5, direction: 'up' }"
        color="green"
        icon="Activity"
      />
      <MetricCard
        title="活跃用户"
        :value="activeUsers.toLocaleString()"
        :trend="{ value: 8.2, direction: 'up' }"
        color="purple"
        icon="Users"
      />
    </div>
    
    <!-- Charts Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Error Trends -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">错误趋势</h3>
        <ErrorTrendsChart :data="errorTrendsData" />
      </div>
      
      <!-- Response Time Distribution -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">响应时间分布</h3>
        <ResponseTimeChart :data="responseTimeData" />
      </div>
      
      <!-- Top Error Messages -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">热门错误消息</h3>
        <TopErrorsChart :data="topErrorsData" />
      </div>
      
      <!-- Service Health -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">服务健康状况</h3>
        <ServiceHealthChart :data="serviceHealthData" />
      </div>
    </div>
    
    <!-- Detailed Tables -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Error Summary Table -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">错误汇总</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  错误类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  数量
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  百分比
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="error in errorSummary" :key="error.type">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                  {{ error.type }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ error.count }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                  {{ error.percentage }}%
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Performance Metrics -->
      <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">性能指标</h3>
        <div class="space-y-4">
          <div v-for="metric in performanceMetrics" :key="metric.name" class="flex items-center justify-between">
            <div>
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ metric.name }}</div>
              <div class="text-xs text-gray-500 dark:text-gray-400">{{ metric.description }}</div>
            </div>
            <div class="text-right">
              <div class="text-sm font-medium text-gray-900 dark:text-white">{{ metric.value }}</div>
              <div 
                class="text-xs"
                :class="metric.trend > 0 ? 'text-red-500' : 'text-green-500'"
              >
                {{ metric.trend > 0 ? '+' : '' }}{{ metric.trend }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- 智能分析 -->
    <div v-if="activeTab === 'intelligent'">
      <LogAnalyzer />
    </div>

    <!-- 可视化分析 -->
    <div v-if="activeTab === 'visualization'">
      <LogVisualization />
    </div>

    <!-- 告警管理 -->
    <div v-if="activeTab === 'alerts'">
      <AlertManager />
    </div>

    <!-- 导出管理 -->
    <div v-if="activeTab === 'export'">
      <ExportManager />
    </div>

    <!-- 高级过滤器 -->
    <div v-if="activeTab === 'filters'">
      <AdvancedFilter />
    </div>

    <!-- 流处理 -->
    <div v-if="activeTab === 'stream'">
      <LogStreamProcessor />
    </div>

    <!-- 团队协作 -->
    <div v-if="activeTab === 'collaboration'">
      <TeamCollaboration />
    </div>

    <!-- 查询构建 -->
    <div v-if="activeTab === 'query'">
      <QueryBuilder />
    </div>

    <!-- 性能监控 -->
    <div v-if="activeTab === 'performance'">
      <PerformanceMonitor />
    </div>

    <!-- 模板管理 -->
    <div v-if="activeTab === 'templates'">
      <LogTemplateManager />
    </div>

    <!-- 安全分析 -->
    <div v-if="activeTab === 'security'">
      <SecurityAnalyzer />
    </div>

    <!-- 智能分类 -->
    <div v-if="activeTab === 'classification'">
      <LogClassifier />
    </div>

    <!-- 关联分析 -->
    <div v-if="activeTab === 'correlation'">
      <LogCorrelation />
    </div>

    <!-- 预测分析 -->
    <div v-if="activeTab === 'prediction'">
      <LogPrediction />
    </div>

    <!-- 索引管理 -->
    <div v-if="activeTab === 'indexing'">
      <LogIndexManager />
    </div>

    <!-- 实时终端 -->
    <div v-if="activeTab === 'livetail'" class="h-full">
      <LiveLogViewer />
    </div>

    <!-- 日志分析 -->
    <div v-if="activeTab === 'analytics'">
      <LogAnalytics />
    </div>

    <!-- 导出管理 -->
    <div v-if="activeTab === 'export'">
      <LogExportManager />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useLogStore } from '@/stores/logStore'
import {
  RefreshCw,
  BarChart3,
  Brain,
  Eye,
  AlertTriangle,
  Download,
  Filter,
  Zap,
  Users,
  Search,
  Activity,
  FileText,
  Shield,
  Tags,
  GitBranch,
  TrendingUp,
  Database,
  Terminal
} from 'lucide-vue-next'

import MetricCard from '@/components/MetricCard.vue'
import ErrorTrendsChart from '@/components/charts/ErrorTrendsChart.vue'
import ResponseTimeChart from '@/components/charts/ResponseTimeChart.vue'
import TopErrorsChart from '@/components/charts/TopErrorsChart.vue'
import ServiceHealthChart from '@/components/charts/ServiceHealthChart.vue'
import LogAnalyzer from '@/components/LogAnalyzer.vue'
import LogVisualization from '@/components/LogVisualization.vue'
import AlertManager from '@/components/AlertManager.vue'
import ExportManager from '@/components/ExportManager.vue'
import AdvancedFilter from '@/components/AdvancedFilter.vue'
import LogStreamProcessor from '@/components/LogStreamProcessor.vue'
import TeamCollaboration from '@/components/TeamCollaboration.vue'
import QueryBuilder from '@/components/QueryBuilder.vue'
import PerformanceMonitor from '@/components/PerformanceMonitor.vue'
import LogTemplateManager from '@/components/LogTemplateManager.vue'
import SecurityAnalyzer from '@/components/SecurityAnalyzer.vue'
import LogClassifier from '@/components/LogClassifier.vue'
import LogCorrelation from '@/components/LogCorrelation.vue'
import LogPrediction from '@/components/LogPrediction.vue'
import LogIndexManager from '@/components/LogIndexManager.vue'
import LiveLogViewer from '@/components/LiveLogViewer.vue'
import LogAnalytics from '@/components/LogAnalytics.vue'
import LogExportManager from '@/components/LogExportManager.vue'

const logStore = useLogStore()

const selectedTimeRange = ref('24h')
const activeTab = ref('overview')

const analyticsTabs = [
  { key: 'overview', label: '概览分析', icon: BarChart3 },
  { key: 'intelligent', label: '智能分析', icon: Brain },
  { key: 'visualization', label: '可视化', icon: Eye },
  { key: 'alerts', label: '告警管理', icon: AlertTriangle },
  { key: 'export', label: '导出管理', icon: Download },
  { key: 'filters', label: '高级过滤', icon: Filter },
  { key: 'stream', label: '流处理', icon: Zap },
  { key: 'collaboration', label: '团队协作', icon: Users },
  { key: 'query', label: '查询构建', icon: Search },
  { key: 'performance', label: '性能监控', icon: Activity },
  { key: 'templates', label: '模板管理', icon: FileText },
  { key: 'security', label: '安全分析', icon: Shield },
  { key: 'classification', label: '智能分类', icon: Tags },
  { key: 'correlation', label: '关联分析', icon: GitBranch },
  { key: 'prediction', label: '预测分析', icon: TrendingUp },
  { key: 'indexing', label: '索引管理', icon: Database },
  { key: 'livetail', label: '实时终端', icon: Terminal },
  { key: 'analytics', label: '日志分析', icon: Activity }
]
const errorTrendsData = ref([])
const responseTimeData = ref([])
const topErrorsData = ref([])
const serviceHealthData = ref([])

// Computed metrics
const errorRate = computed(() => {
  const totalLogs = logStore.logs.length
  const errorLogs = logStore.logLevelCounts.error
  return totalLogs > 0 ? ((errorLogs / totalLogs) * 100).toFixed(1) : 0
})

const avgResponseTime = computed(() => {
  // Mock calculation - in real app, this would come from log metadata
  return Math.floor(Math.random() * 200) + 50
})

const throughput = computed(() => {
  // Mock calculation - logs per minute
  return Math.floor(Math.random() * 1000) + 500
})

const activeUsers = computed(() => {
  // Mock calculation - unique users from metadata
  return Math.floor(Math.random() * 5000) + 1000
})

const errorSummary = computed(() => [
  { type: '连接超时', count: 45, percentage: 35.2 },
  { type: '身份验证失败', count: 32, percentage: 25.0 },
  { type: '数据库错误', count: 28, percentage: 21.9 },
  { type: '内存分配失败', count: 15, percentage: 11.7 },
  { type: '其他', count: 8, percentage: 6.2 }
])

const performanceMetrics = computed(() => [
  {
    name: 'P95 响应时间',
    description: '95%分位响应时间',
    value: '245ms',
    trend: -5.2
  },
  {
    name: 'P99 响应时间',
    description: '99%分位响应时间',
    value: '892ms',
    trend: 2.1
  },
  {
    name: '错误率',
    description: '失败请求百分比',
    value: '2.3%',
    trend: -1.8
  },
  {
    name: '吞吐量',
    description: '每分钟请求数',
    value: '1,247/分钟',
    trend: 8.5
  }
])

const refreshAnalytics = () => {
  // Generate mock data for charts
  generateMockChartData()
}

const generateMockChartData = () => {
  // Mock error trends data
  errorTrendsData.value = Array.from({ length: 24 }, (_, i) => ({
    time: `${String(i).padStart(2, '0')}:00`,
    errors: Math.floor(Math.random() * 50),
    warnings: Math.floor(Math.random() * 100)
  }))
  
  // Mock response time data
  responseTimeData.value = Array.from({ length: 10 }, (_, i) => ({
    range: `${i * 100}-${(i + 1) * 100}ms`,
    count: Math.floor(Math.random() * 200) + 50
  }))
  
  // Mock top errors data
  topErrorsData.value = [
    { message: 'Connection timeout to database', count: 45 },
    { message: 'Failed to authenticate user', count: 32 },
    { message: 'Memory allocation failed', count: 28 },
    { message: 'Invalid JSON payload', count: 15 },
    { message: 'Rate limit exceeded', count: 12 }
  ]
  
  // Mock service health data
  serviceHealthData.value = [
    { service: 'API Gateway', status: 'healthy', uptime: 99.9 },
    { service: 'Auth Service', status: 'healthy', uptime: 99.7 },
    { service: 'Database', status: 'warning', uptime: 98.5 },
    { service: 'Cache', status: 'healthy', uptime: 99.8 },
    { service: 'Payment', status: 'error', uptime: 95.2 }
  ]
}

onMounted(() => {
  generateMockChartData()
})
</script>
