<template>
  <div class="live-logs-page h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
    <!-- 页面头部 -->
    <div class="page-header bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-3">
            <component :is="Terminal" class="w-6 h-6 text-primary-600" />
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">实时日志终端</h1>
          </div>
          
          <div class="flex items-center space-x-2">
            <div 
              class="w-3 h-3 rounded-full animate-pulse"
              :class="connectionStatus ? 'bg-green-500' : 'bg-red-500'"
            ></div>
            <span class="text-sm text-gray-600 dark:text-gray-400">
              {{ connectionStatus ? '实时连接' : '连接断开' }}
            </span>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <!-- 快速操作 -->
          <div class="flex items-center space-x-2">
            <button
              @click="toggleTheme"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="切换主题"
            >
              <component :is="isDarkMode ? Sun : Moon" class="w-5 h-5" />
            </button>
            
            <button
              @click="openSettings"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="设置"
            >
              <component :is="Settings" class="w-5 h-5" />
            </button>
            
            <button
              @click="openHelp"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="帮助"
            >
              <component :is="HelpCircle" class="w-5 h-5" />
            </button>
          </div>
          
          <div class="h-6 w-px bg-gray-300 dark:bg-gray-600"></div>
          
          <!-- 连接状态和统计 -->
          <div class="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
            <div class="flex items-center space-x-2">
              <component :is="Activity" class="w-4 h-4" />
              <span>{{ logRate }}/秒</span>
            </div>
            
            <div class="flex items-center space-x-2">
              <component :is="Database" class="w-4 h-4" />
              <span>{{ formatBytes(dataSize) }}</span>
            </div>
            
            <div class="flex items-center space-x-2">
              <component :is="Clock" class="w-4 h-4" />
              <span>{{ uptime }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="flex-1 overflow-hidden">
      <LiveLogViewer />
    </div>
    
    <!-- 快捷键提示 -->
    <div 
      v-if="showShortcuts"
      class="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 max-w-sm"
    >
      <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-semibold text-gray-900 dark:text-white">快捷键</h3>
        <button
          @click="showShortcuts = false"
          class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
        >
          <component :is="X" class="w-4 h-4" />
        </button>
      </div>
      
      <div class="space-y-2 text-xs text-gray-600 dark:text-gray-400">
        <div class="flex justify-between">
          <span>清空终端</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+L</kbd>
        </div>
        <div class="flex justify-between">
          <span>搜索</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+F</kbd>
        </div>
        <div class="flex justify-between">
          <span>全屏</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">F11</kbd>
        </div>
        <div class="flex justify-between">
          <span>暂停/继续</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Space</kbd>
        </div>
        <div class="flex justify-between">
          <span>跳转到底部</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">End</kbd>
        </div>
        <div class="flex justify-between">
          <span>跳转到顶部</span>
          <kbd class="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Home</kbd>
        </div>
      </div>
    </div>
    
    <!-- 设置弹窗 -->
    <div 
      v-if="showSettingsModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="showSettingsModal = false"
    >
      <div 
        class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-h-96 overflow-y-auto"
        @click.stop
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white">终端设置</h3>
          <button
            @click="showSettingsModal = false"
            class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
          >
            <component :is="X" class="w-5 h-5" />
          </button>
        </div>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              字体大小
            </label>
            <select v-model="settings.fontSize" class="input">
              <option value="12">12px</option>
              <option value="13">13px</option>
              <option value="14">14px</option>
              <option value="16">16px</option>
              <option value="18">18px</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              字体族
            </label>
            <select v-model="settings.fontFamily" class="input">
              <option value="Monaco">Monaco</option>
              <option value="Menlo">Menlo</option>
              <option value="Consolas">Consolas</option>
              <option value="Ubuntu Mono">Ubuntu Mono</option>
              <option value="Courier New">Courier New</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              缓冲区大小
            </label>
            <select v-model="settings.bufferSize" class="input">
              <option value="1000">1,000 行</option>
              <option value="5000">5,000 行</option>
              <option value="10000">10,000 行</option>
              <option value="50000">50,000 行</option>
            </select>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-700 dark:text-gray-300">显示时间戳</span>
            <button
              @click="settings.showTimestamp = !settings.showTimestamp"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="settings.showTimestamp ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="settings.showTimestamp ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-700 dark:text-gray-300">自动滚动</span>
            <button
              @click="settings.autoScroll = !settings.autoScroll"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="settings.autoScroll ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="settings.autoScroll ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm text-gray-700 dark:text-gray-300">声音提示</span>
            <button
              @click="settings.soundEnabled = !settings.soundEnabled"
              class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
              :class="settings.soundEnabled ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-700'"
            >
              <span
                class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"
                :class="settings.soundEnabled ? 'translate-x-5' : 'translate-x-0'"
              ></span>
            </button>
          </div>
        </div>
        
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="resetSettings"
            class="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
          >
            重置
          </button>
          <button
            @click="saveSettings"
            class="px-4 py-2 text-sm bg-primary-600 text-white rounded hover:bg-primary-700"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/stores/appStore'
import { 
  Terminal, 
  Sun, 
  Moon, 
  Settings, 
  HelpCircle, 
  Activity, 
  Database, 
  Clock, 
  X 
} from 'lucide-vue-next'

import LiveLogViewer from '@/components/LiveLogViewer.vue'

const appStore = useAppStore()

// 页面状态
const connectionStatus = ref(true)
const isDarkMode = ref(false)
const showShortcuts = ref(false)
const showSettingsModal = ref(false)

// 统计数据
const logRate = ref(0)
const dataSize = ref(0)
const uptime = ref('00:00:00')

// 设置
const settings = reactive({
  fontSize: 14,
  fontFamily: 'Monaco',
  bufferSize: 10000,
  showTimestamp: true,
  autoScroll: true,
  soundEnabled: false
})

let uptimeInterval = null
let statsInterval = null

const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
}

const openSettings = () => {
  showSettingsModal.value = true
}

const openHelp = () => {
  showShortcuts.value = true
}

const saveSettings = () => {
  localStorage.setItem('liveLogSettings', JSON.stringify(settings))
  showSettingsModal.value = false
  appStore.addNotification({
    type: 'success',
    message: '设置已保存'
  })
}

const resetSettings = () => {
  Object.assign(settings, {
    fontSize: 14,
    fontFamily: 'Monaco',
    bufferSize: 10000,
    showTimestamp: true,
    autoScroll: true,
    soundEnabled: false
  })
}

const loadSettings = () => {
  const saved = localStorage.getItem('liveLogSettings')
  if (saved) {
    Object.assign(settings, JSON.parse(saved))
  }
}

const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const updateStats = () => {
  // 模拟统计数据更新
  logRate.value = Math.floor(Math.random() * 50) + 10
  dataSize.value = Math.floor(Math.random() * 10000000) + 5000000
}

const updateUptime = () => {
  const start = Date.now()
  uptimeInterval = setInterval(() => {
    const elapsed = Date.now() - start
    const hours = Math.floor(elapsed / 3600000)
    const minutes = Math.floor((elapsed % 3600000) / 60000)
    const seconds = Math.floor((elapsed % 60000) / 1000)
    
    uptime.value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  }, 1000)
}

// 键盘快捷键
const handleKeydown = (event) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case 'l':
        event.preventDefault()
        // 清空终端
        break
      case 'f':
        event.preventDefault()
        // 打开搜索
        break
    }
  } else {
    switch (event.key) {
      case 'F11':
        event.preventDefault()
        // 切换全屏
        break
      case ' ':
        event.preventDefault()
        // 暂停/继续
        break
      case 'End':
        event.preventDefault()
        // 跳转到底部
        break
      case 'Home':
        event.preventDefault()
        // 跳转到顶部
        break
      case '?':
        showShortcuts.value = !showShortcuts.value
        break
    }
  }
}

onMounted(() => {
  loadSettings()
  updateUptime()
  
  // 启动统计更新
  statsInterval = setInterval(updateStats, 2000)
  
  // 绑定键盘事件
  document.addEventListener('keydown', handleKeydown)
  
  // 检查系统主题
  isDarkMode.value = document.documentElement.classList.contains('dark')
})

onUnmounted(() => {
  if (uptimeInterval) clearInterval(uptimeInterval)
  if (statsInterval) clearInterval(statsInterval)
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.live-logs-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-header {
  min-height: 72px;
}

kbd {
  font-family: inherit;
  font-size: 0.75rem;
}
</style>
